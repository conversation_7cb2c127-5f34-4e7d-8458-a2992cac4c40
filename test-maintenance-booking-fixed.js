const { chromium } = require('playwright');

async function testMaintenanceBookingFixed() {
  console.log('🧪 Testing CreateMaintenanceBookingTool with correct parameter schema...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to chat interface
    await page.goto('http://localhost:3001');
    console.log('✅ Navigated to chat interface');

    // Wait for connection
    await page.waitForSelector('text=ONLINE', { timeout: 10000 });
    console.log('✅ Chat interface is ONLINE');

    // Test CreateMaintenanceBookingTool with correct parameters
    const testMessage = 'Create a maintenance booking for aircraft AC-A from 2025-01-15T09:00:00Z to 2025-01-15T17:00:00Z for engine inspection';
    
    console.log(`📝 Sending message: "${testMessage}"`);
    
    // Type the message
    await page.fill('input[type="text"]', testMessage);
    await page.press('input[type="text"]', 'Enter');

    // Wait for "Thinking..." to appear
    await page.waitForSelector('text=Thinking...', { timeout: 5000 });
    console.log('✅ LLM is processing the request');

    // Wait for response (up to 30 seconds)
    await page.waitForSelector('text=Thinking...', { state: 'detached', timeout: 30000 });
    console.log('✅ LLM finished processing');

    // Get the response
    const messages = await page.locator('.message').all();
    const lastMessage = messages[messages.length - 1];
    const responseText = await lastMessage.textContent();
    
    console.log('📋 LLM Response:', responseText);

    // Check for success indicators
    if (responseText.includes('successfully') || responseText.includes('created') || responseText.includes('booked')) {
      console.log('🎉 SUCCESS: CreateMaintenanceBookingTool appears to be working!');
      
      // Now verify in database using PostgreSQL MCP tool
      console.log('🔍 Verifying booking in database...');
      
      const verifyMessage = 'Use the postgres tool to check if there are any maintenance bookings for aircraft AC-A in the database';
      await page.fill('input[type="text"]', verifyMessage);
      await page.press('input[type="text"]', 'Enter');
      
      // Wait for database verification response
      await page.waitForSelector('text=Thinking...', { timeout: 5000 });
      await page.waitForSelector('text=Thinking...', { state: 'detached', timeout: 30000 });
      
      const verifyMessages = await page.locator('.message').all();
      const verifyResponse = await verifyMessages[verifyMessages.length - 1].textContent();
      console.log('📊 Database verification:', verifyResponse);
      
      if (verifyResponse.includes('AC-A') || verifyResponse.includes('maintenance') || verifyResponse.includes('booking')) {
        console.log('🎯 COMPLETE SUCCESS: Booking verified in database!');
      } else {
        console.log('⚠️  Database verification inconclusive');
      }
      
    } else if (responseText.includes('Error') || responseText.includes('error') || responseText.includes('failed')) {
      console.log('❌ FAILED: Tool execution failed');
      console.log('Error details:', responseText);
    } else {
      console.log('❓ UNCLEAR: Response doesn\'t clearly indicate success or failure');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  } finally {
    await browser.close();
  }
}

testMaintenanceBookingFixed().catch(console.error);
