FROM ruby:3.3.7-alpine

# Install system dependencies including Node.js, build tools and curl for health checks
RUN apk add --no-cache curl build-base nodejs npm

WORKDIR /app

# Copy Gemfile and install gems
COPY Gemfile Gemfile.lock ./
RUN bundle install

# Copy application code
COPY . .

# Make scripts executable
RUN chmod +x bin/server_http
RUN chmod +x bin/server_stdio
RUN chmod +x bridge-server.js
RUN chmod +x docker-entrypoint.sh

# Expose port
EXPOSE 8080

# Use the entrypoint script
CMD ["./docker-entrypoint.sh"]
