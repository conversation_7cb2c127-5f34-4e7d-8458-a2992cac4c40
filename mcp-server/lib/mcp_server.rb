#!/usr/bin/env ruby
# frozen_string_literal: true

require 'fast_mcp'
require_relative 'tools/list_aircraft_tool'
require_relative 'tools/find_aircraft_tool'
require_relative 'tools/create_maintenance_booking_tool'
require_relative 'tools/check_maintenance_conflicts_tool'

class MCPServer
  def self.create_server
    server = FastMcp::Server.new(
      name: 'FlightLogger MCP Server',
      version: '1.0.0'
    )

    # Register all tools
    server.register_tool(ListAircraftTool)
    server.register_tool(FindAircraftTool)
    server.register_tool(CreateMaintenanceBookingTool)
    server.register_tool(CheckMaintenanceConflictsTool)

    server
  end

  def self.start_stdio
    server = create_server
    server.start
  end

  def self.start_http(port = 8080)
    puts "Starting FlightLogger MCP Server (HTTP) on port #{port}..."
    $stdout.flush

    server = create_server
    puts "MCP Server created with #{server.tools.length} tools"
    $stdout.flush

    # For now, use STDIO mode since HTTP transport is complex
    puts "Starting in STDIO mode (HTTP transport will be added later)..."
    $stdout.flush

    server.start
  end
end
