require 'fast_mcp'
require 'json'
require_relative 'flightlogger/client'



# FlightLogger MCP Tools - Core functionality for aircraft management and maintenance booking

class ListAircraftTool < FastMcp::Tool
  description 'List all aircraft in the fleet'

  def call
    client = FlightLogger::Client.new
    aircraft_data = client.list_aircraft

    if aircraft_data[:success]
      {
        success: true,
        aircraft: aircraft_data[:aircraft],
        message: "Found #{aircraft_data[:aircraft].length} aircraft"
      }
    else
      {
        success: false,
        message: aircraft_data[:message] || 'Failed to retrieve aircraft data'
      }
    end
  end
end

class FindAircraftTool < FastMcp::Tool
  description 'Find a specific aircraft by name or search term'

  arguments do
    required(:search_term).filled(:string).description('Aircraft name or search term')
  end

  def call(search_term:)
    client = FlightLogger::Client.new
    aircraft_data = client.find_aircraft(search_term)

    if aircraft_data[:success]
      {
        success: true,
        aircraft: aircraft_data[:aircraft],
        message: "Found aircraft matching: #{search_term}"
      }
    else
      {
        success: false,
        message: aircraft_data[:message] || 'Aircraft not found'
      }
    end
  end
end

class CreateMaintenanceBookingTool < FastMcp::Tool
  description 'Create a maintenance booking for an aircraft with conflict checking'

  arguments do
    required(:aircraft_call_sign).filled(:string).description('Call sign of the aircraft')
    required(:start_time).filled(:string).description('Start time (ISO 8601 format)')
    required(:end_time).filled(:string).description('End time (ISO 8601 format)')
    optional(:description).filled(:string).description('Description of maintenance work')
    optional(:maintenance_type).filled(:string).description('Type of maintenance')
    optional(:check_conflicts).filled(:bool).description('Whether to check for conflicts (default: true)')
  end

  def call(aircraft_call_sign:, start_time:, end_time:, description: nil, maintenance_type: nil, check_conflicts: true)
    client = FlightLogger::Client.new

    # First find the aircraft to get its ID
    aircraft_data = client.find_aircraft(aircraft_call_sign)

    unless aircraft_data[:success] && aircraft_data[:aircraft]&.any?
      return {
        success: false,
        message: "Aircraft '#{aircraft_call_sign}' not found"
      }
    end

    aircraft = aircraft_data[:aircraft].first
    aircraft_id = aircraft['id']

    # Use conflict checking method if requested
    result = if check_conflicts
               client.create_maintenance_booking_with_conflict_check(
                 aircraft_id: aircraft_id,
                 booking_start: start_time,
                 booking_end: end_time,
                 comment: description || 'Scheduled maintenance',
                 notify_via_email: false
               )
             else
               client.create_maintenance_booking(
                 aircraft_id: aircraft_id,
                 booking_start: start_time,
                 booking_end: end_time,
                 comment: description || 'Scheduled maintenance',
                 notify_via_email: false
               )
             end

    if result[:success]
      {
        success: true,
        booking: result[:booking],
        aircraft: aircraft,
        message: "Maintenance booking created successfully for #{aircraft_call_sign}"
      }
    else
      {
        success: false,
        message: result[:message] || 'Failed to create maintenance booking'
      }
    end
  end
end

class CheckMaintenanceConflictsTool < FastMcp::Tool
  description 'Check for maintenance scheduling conflicts for a specific aircraft and time period'

  arguments do
    required(:aircraft_call_sign).filled(:string).description('Aircraft call sign to check')
    required(:start_time).filled(:string).description('Start time to check (ISO 8601 format)')
    required(:end_time).filled(:string).description('End time to check (ISO 8601 format)')
  end

  def call(aircraft_call_sign:, start_time:, end_time:)
    client = FlightLogger::Client.new

    # First find the aircraft to get its ID
    aircraft_data = client.find_aircraft(aircraft_call_sign)

    unless aircraft_data[:success] && aircraft_data[:aircraft]&.any?
      return {
        success: false,
        message: "Aircraft '#{aircraft_call_sign}' not found"
      }
    end

    aircraft = aircraft_data[:aircraft].first
    aircraft_id = aircraft['id']

    result = client.check_maintenance_conflicts(
      aircraft_id: aircraft_id,
      start_time: start_time,
      end_time: end_time
    )

    if result[:success]
      {
        success: true,
        conflicts: result[:conflicts] || [],
        aircraft: aircraft,
        message: result[:message]
      }
    else
      {
        success: false,
        message: result[:message] || 'Failed to check conflicts'
      }
    end
  end
end

# FlightLogger MCP Resources - Provide structured data access

class AircraftFleetResource < FastMcp::Resource
  uri 'flightlogger://fleet/aircraft'
  resource_name 'Aircraft Fleet'
  description 'Current aircraft fleet information'
  mime_type 'application/json'

  def content
    client = FlightLogger::Client.new
    aircraft_data = client.list_aircraft

    if aircraft_data[:success]
      JSON.generate({
                      fleet_size: aircraft_data[:aircraft].length,
                      aircraft: aircraft_data[:aircraft],
                      last_updated: Time.now.iso8601
                    })
    else
      JSON.generate({
                      error: 'Failed to retrieve fleet data',
                      message: aircraft_data[:message]
                    })
    end
  end
end

class MaintenanceScheduleResource < FastMcp::Resource
  uri 'flightlogger://maintenance/schedule'
  resource_name 'Maintenance Schedule'
  description 'Current maintenance schedule and bookings'
  mime_type 'application/json'

  def content
    # Return basic maintenance schedule information
    JSON.generate({
                    upcoming_maintenance: [],
                    active_bookings: [],
                    last_updated: Time.now.iso8601,
                    note: 'Maintenance scheduling system ready'
                  })
  end
end

# FlightLogger MCP Server - Clean FastMCP implementation
class FlightLoggerMCPServer
  def self.create_server
    server = FastMcp::Server.new(name: 'flightlogger-mcp', version: '1.0.0')

    # Register the 4 core tools for aircraft management and maintenance booking
    server.register_tool(ListAircraftTool)
    server.register_tool(FindAircraftTool)
    server.register_tool(CreateMaintenanceBookingTool)
    server.register_tool(CheckMaintenanceConflictsTool)

    # Register resources for structured data access
    server.register_resource(AircraftFleetResource)
    server.register_resource(MaintenanceScheduleResource)

    puts "Registered #{server.tools.length} tools and #{server.resources.length} resources"
    server
  end

  def self.create_server
    # Create the MCP server instance
    server = FastMcp::Server.new(name: 'flightlogger-mcp', version: '1.0.0')

    # Register the 4 core tools for aircraft management and maintenance booking
    server.register_tool(ListAircraftTool)
    server.register_tool(FindAircraftTool)
    server.register_tool(CreateMaintenanceBookingTool)
    server.register_tool(CheckMaintenanceConflictsTool)

    # Register resources for structured data access
    server.register_resource(AircraftFleetResource)
    server.register_resource(MaintenanceScheduleResource)

    puts "Registered #{server.tools.length} tools and #{server.resources.length} resources"
    server
  end

  def self.start_stdio
    server = create_server

    puts 'Starting FlightLogger MCP Assistant (STDIO transport)'
    puts "Tools: #{server.tools.length} registered"
    puts "Resources: #{server.resources.length} registered"

    # Use FastMCP's built-in STDIO transport
    server.start
  end

  def self.start_http
    require 'rack'
    require 'rackup'
    require 'json'

    port = ENV.fetch('MCP_PORT', 8080).to_i
    host = ENV.fetch('MCP_HOST', '0.0.0.0')

    puts "Starting FlightLogger MCP Assistant (HTTP transport) on #{host}:#{port}"

    # Create the MCP server instance
    server = create_server

    # Create a simple HTTP wrapper around the MCP server
    app = lambda do |env|
      request = Rack::Request.new(env)

      # Add CORS headers
      headers = {
        'Content-Type' => 'application/json',
        'Access-Control-Allow-Origin' => '*',
        'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers' => 'Content-Type, Origin'
      }

      case [request.request_method, request.path_info]
      when ['OPTIONS', '/mcp/messages']
        [200, headers, ['']]
      when ['GET', '/health']
        [200, headers, [JSON.generate({ status: 'healthy', timestamp: Time.now.iso8601 })]]
      when ['POST', '/mcp/messages']
        begin
          # Parse the JSON-RPC request
          body = request.body.read
          rpc_request = JSON.parse(body)

          # Handle the MCP request
          case rpc_request['method']
          when 'tools/list'
            tools = server.tools.map do |tool_name, tool_class|
              # Get tool metadata from the FastMCP tool class
              {
                name: tool_name,
                description: tool_class.instance_variable_get(:@description) || 'No description',
                inputSchema: {
                  type: 'object',
                  properties: {},
                  required: []
                }
              }
            end
            response = {
              jsonrpc: '2.0',
              id: rpc_request['id'],
              result: { tools: tools }
            }
          when 'tools/call'
            tool_name = rpc_request.dig('params', 'name')
            arguments = rpc_request.dig('params', 'arguments') || {}

            # Find and call the tool
            if server.tools.key?(tool_name)
              begin
                tool_class = server.tools[tool_name]
                tool_instance = tool_class.new
                result = tool_instance.call(**arguments.transform_keys(&:to_sym))

                response = {
                  jsonrpc: '2.0',
                  id: rpc_request['id'],
                  result: { content: [{ type: 'text', text: result.to_json }] }
                }
              rescue => e
                response = {
                  jsonrpc: '2.0',
                  id: rpc_request['id'],
                  error: { code: -32603, message: "Tool execution error: #{e.message}" }
                }
              end
            else
              response = {
                jsonrpc: '2.0',
                id: rpc_request['id'],
                error: { code: -32601, message: "Tool not found: #{tool_name}" }
              }
            end
          else
            response = {
              jsonrpc: '2.0',
              id: rpc_request['id'],
              error: { code: -32601, message: "Method not found: #{rpc_request['method']}" }
            }
          end

          [200, headers, [JSON.generate(response)]]
        rescue JSON::ParserError
          error_response = {
            jsonrpc: '2.0',
            id: nil,
            error: { code: -32700, message: 'Parse error' }
          }
          [400, headers, [JSON.generate(error_response)]]
        rescue => e
          error_response = {
            jsonrpc: '2.0',
            id: rpc_request&.dig('id'),
            error: { code: -32603, message: "Internal error: #{e.message}" }
          }
          [500, headers, [JSON.generate(error_response)]]
        end
      when ['GET', '/']
        info = {
          service: 'FlightLogger MCP Server',
          version: '1.0.0',
          tools: server.tools.length,
          resources: server.resources.length,
          endpoints: {
            health: '/health',
            mcp_messages: '/mcp/messages'
          }
        }
        [200, headers, [JSON.generate(info)]]
      else
        [404, headers, [JSON.generate({ error: 'Not found' })]]
      end
    end

    # Start the server
    puts "Health endpoint: http://#{host}:#{port}/health"
    puts "MCP Messages endpoint: http://#{host}:#{port}/mcp/messages"
    puts "Server info: http://#{host}:#{port}/"

    Rackup::Handler.default.run(app, Host: host, Port: port)
  end

  private



  def self.flightlogger_client
    @flightlogger_client ||= begin
      # Determine FlightLogger API URL based on environment
      # In Docker: use service name 'app' instead of localhost
      # For local development: fallback to localhost:3000
      base_url = if ENV['DOCKER_ENV'] == 'true' || system('docker ps --format "table {{.Names}}" | grep -q "^app$"')
                   subdomain = ENV.fetch('FLIGHTLOGGER_SUBDOMAIN', 'demo')
                   "http://app:3000"
                 else
                   subdomain = ENV.fetch('FLIGHTLOGGER_SUBDOMAIN', 'demo')
                   "http://#{subdomain}.flightlogger.test:3000"
                 end

      puts "Connecting to FlightLogger API at: #{base_url}"

      # Create GraphQL client with authentication
      GraphQL::Client.new(
        execute: GraphQL::Client::HTTP.new("#{base_url}/graphql") do
          def headers(_context)
            # Use demo token for local development
            token = ENV.fetch('FLIGHTLOGGER_TOKEN', 'demo_token_for_local_development')
            { 'Authorization' => "Bearer #{token}" }
          end
        end,
        schema: FlightLoggerSchema
      )
    rescue StandardError => e
      puts "Warning: Could not connect to FlightLogger API: #{e.message}"
      puts "MCP server will return error messages instead of mock data"
      nil
    end
  end
end

# Main entry point - determine transport based on environment
if __FILE__ == $PROGRAM_NAME
  transport = ENV.fetch('MCP_TRANSPORT', 'stdio').downcase

  case transport
  when 'http'
    FlightLoggerMCPServer.start_http
  when 'stdio'
    FlightLoggerMCPServer.start_stdio
  else
    puts "Unknown transport: #{transport}. Use 'stdio' or 'http'"
    exit 1
  end
end
