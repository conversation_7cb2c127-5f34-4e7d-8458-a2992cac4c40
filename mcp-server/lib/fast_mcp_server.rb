require 'fast_mcp'
require 'json'
require 'rack/cors'
require_relative 'flightlogger/client'



# FlightLogger MCP Tools - Core functionality for aircraft management and maintenance booking

class ListAircraftTool < FastMcp::Tool
  description 'List all aircraft in the fleet'

  def call
    client = FlightLogger::Client.new
    aircraft_data = client.list_aircraft

    if aircraft_data[:success]
      {
        success: true,
        aircraft: aircraft_data[:aircraft],
        message: "Found #{aircraft_data[:aircraft].length} aircraft"
      }
    else
      {
        success: false,
        message: aircraft_data[:message] || 'Failed to retrieve aircraft data'
      }
    end
  end
end

class FindAircraftTool < FastMcp::Tool
  description 'Find a specific aircraft by name or search term'

  arguments do
    required(:search_term).filled(:string).description('Aircraft name or search term')
  end

  def call(search_term:)
    client = FlightLogger::Client.new
    aircraft_data = client.find_aircraft(search_term)

    if aircraft_data[:success]
      {
        success: true,
        aircraft: aircraft_data[:aircraft],
        message: "Found aircraft matching: #{search_term}"
      }
    else
      {
        success: false,
        message: aircraft_data[:message] || 'Aircraft not found'
      }
    end
  end
end

class CreateMaintenanceBookingTool < FastMcp::Tool
  description 'Create a maintenance booking for an aircraft with conflict checking'

  arguments do
    required(:aircraft_call_sign).filled(:string).description('Call sign of the aircraft')
    required(:start_time).filled(:string).description('Start time (ISO 8601 format)')
    required(:end_time).filled(:string).description('End time (ISO 8601 format)')
    optional(:description).filled(:string).description('Description of maintenance work')
    optional(:maintenance_type).filled(:string).description('Type of maintenance')
    optional(:check_conflicts).filled(:bool).description('Whether to check for conflicts (default: true)')
  end

  def call(aircraft_call_sign:, start_time:, end_time:, description: nil, maintenance_type: nil, check_conflicts: true)
    client = FlightLogger::Client.new

    # First find the aircraft to get its ID
    aircraft_data = client.find_aircraft(aircraft_call_sign)

    unless aircraft_data[:success] && aircraft_data[:aircraft]&.any?
      return {
        success: false,
        message: "Aircraft '#{aircraft_call_sign}' not found"
      }
    end

    aircraft = aircraft_data[:aircraft].first
    aircraft_id = aircraft['id']

    # Use conflict checking method if requested
    result = if check_conflicts
               client.create_maintenance_booking_with_conflict_check(
                 aircraft_id: aircraft_id,
                 booking_start: start_time,
                 booking_end: end_time,
                 comment: description || 'Scheduled maintenance',
                 notify_via_email: false
               )
             else
               client.create_maintenance_booking(
                 aircraft_id: aircraft_id,
                 booking_start: start_time,
                 booking_end: end_time,
                 comment: description || 'Scheduled maintenance',
                 notify_via_email: false
               )
             end

    if result[:success]
      {
        success: true,
        booking: result[:booking],
        aircraft: aircraft,
        message: "Maintenance booking created successfully for #{aircraft_call_sign}"
      }
    else
      {
        success: false,
        message: result[:message] || 'Failed to create maintenance booking'
      }
    end
  end
end

class CheckMaintenanceConflictsTool < FastMcp::Tool
  description 'Check for maintenance scheduling conflicts for a specific aircraft and time period'

  arguments do
    required(:aircraft_call_sign).filled(:string).description('Aircraft call sign to check')
    required(:start_time).filled(:string).description('Start time to check (ISO 8601 format)')
    required(:end_time).filled(:string).description('End time to check (ISO 8601 format)')
  end

  def call(aircraft_call_sign:, start_time:, end_time:)
    client = FlightLogger::Client.new

    # First find the aircraft to get its ID
    aircraft_data = client.find_aircraft(aircraft_call_sign)

    unless aircraft_data[:success] && aircraft_data[:aircraft]&.any?
      return {
        success: false,
        message: "Aircraft '#{aircraft_call_sign}' not found"
      }
    end

    aircraft = aircraft_data[:aircraft].first
    aircraft_id = aircraft['id']

    result = client.check_maintenance_conflicts(
      aircraft_id: aircraft_id,
      start_time: start_time,
      end_time: end_time
    )

    if result[:success]
      {
        success: true,
        conflicts: result[:conflicts] || [],
        aircraft: aircraft,
        message: result[:message]
      }
    else
      {
        success: false,
        message: result[:message] || 'Failed to check conflicts'
      }
    end
  end
end

# FlightLogger MCP Resources - Provide structured data access

class AircraftFleetResource < FastMcp::Resource
  uri 'flightlogger://fleet/aircraft'
  resource_name 'Aircraft Fleet'
  description 'Current aircraft fleet information'
  mime_type 'application/json'

  def content
    client = FlightLogger::Client.new
    aircraft_data = client.list_aircraft

    if aircraft_data[:success]
      JSON.generate({
                      fleet_size: aircraft_data[:aircraft].length,
                      aircraft: aircraft_data[:aircraft],
                      last_updated: Time.now.iso8601
                    })
    else
      JSON.generate({
                      error: 'Failed to retrieve fleet data',
                      message: aircraft_data[:message]
                    })
    end
  end
end

class MaintenanceScheduleResource < FastMcp::Resource
  uri 'flightlogger://maintenance/schedule'
  resource_name 'Maintenance Schedule'
  description 'Current maintenance schedule and bookings'
  mime_type 'application/json'

  def content
    # Return basic maintenance schedule information
    JSON.generate({
                    upcoming_maintenance: [],
                    active_bookings: [],
                    last_updated: Time.now.iso8601,
                    note: 'Maintenance scheduling system ready'
                  })
  end
end

# FlightLogger MCP Server - Clean FastMCP implementation
class FlightLoggerMCPServer


  def self.start_stdio
    server = create_server

    puts 'Starting FlightLogger MCP Assistant (STDIO transport)'
    puts "Tools: #{server.tools.length} registered"
    puts "Resources: #{server.resources.length} registered"

    # Use FastMCP's built-in STDIO transport
    server.start
  end

  def self.start_http
    require 'rack'
    require 'rackup'

    port = ENV.fetch('MCP_PORT', 8080).to_i
    host = ENV.fetch('MCP_HOST', '0.0.0.0')

    puts "Starting FlightLogger MCP Assistant (HTTP transport) on #{host}:#{port}"

    # Create a default app for the middleware to wrap
    default_app = lambda { |_env|
      [404, { 'Content-Type' => 'text/plain' }, ['Not Found']]
    }

    # Create a simple default app
    default_app = lambda { |_env|
      [404, { 'Content-Type' => 'text/plain' }, ['Not Found']]
    }

    # Create FastMCP middleware with proper configuration for Docker environments
    # Based on the documentation, we need to disable localhost_only and configure allowed IPs
    mcp_middleware = FastMcp.rack_middleware(
      default_app,
      name: 'flightlogger-mcp',
      version: '1.0.0',
      localhost_only: false,  # Critical: Allow connections from other containers
      allowed_ips: ['127.0.0.1', '::1', '**********/16', '0.0.0.0/0'],  # Allow Docker network and all IPs
      allowed_origins: ['*']  # Allow all origins for Docker environment
    ) do |server|
      # Register the 4 core tools for aircraft management and maintenance booking
      server.register_tool(ListAircraftTool)
      server.register_tool(FindAircraftTool)
      server.register_tool(CreateMaintenanceBookingTool)
      server.register_tool(CheckMaintenanceConflictsTool)

      # Register resources for structured data access
      server.register_resource(AircraftFleetResource)
      server.register_resource(MaintenanceScheduleResource)

      puts "Registered #{server.tools.length} tools and #{server.resources.length} resources"
    end

    # Create Rack app with CORS and health endpoint
    rack_app = Rack::Builder.new do
      use Rack::Cors do
        allow do
          origins '*'
          resource '*', headers: :any, methods: %i[get post options]
        end
      end

      map '/health' do
        run lambda { |_env|
          [200, { 'Content-Type' => 'application/json' },
           [JSON.generate({ status: 'healthy', timestamp: Time.now.iso8601 })]]
        }
      end

      # Use the FastMCP middleware for all other routes
      run mcp_middleware
    end

    # Start the Rack server
    puts 'Starting Rack server...'
    puts "Health endpoint: http://#{host}:#{port}/health"

    Rackup::Handler.default.run(rack_app, Host: host, Port: port)
  end
end

# Main entry point - determine transport based on environment
if __FILE__ == $PROGRAM_NAME
  transport = ENV.fetch('MCP_TRANSPORT', 'stdio').downcase

  case transport
  when 'http'
    FlightLoggerMCPServer.start_http
  when 'stdio'
    FlightLoggerMCPServer.start_stdio
  else
    puts "Unknown transport: #{transport}. Use 'stdio' or 'http'"
    exit 1
  end
end
