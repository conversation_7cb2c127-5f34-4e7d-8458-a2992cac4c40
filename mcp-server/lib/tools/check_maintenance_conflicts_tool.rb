# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class CheckMaintenanceConflictsTool < FastMcp::Tool
  description 'Check for maintenance scheduling conflicts for a specific aircraft and time period'

  arguments do
    required(:aircraft_call_sign).filled(:string).description("Aircraft call sign (e.g., 'AC-A')")
    required(:start_time).filled(:string).description("Start time in ISO format (YYYY-MM-DDTHH:MM:SSZ)")
    required(:end_time).filled(:string).description("End time in ISO format (YYYY-MM-DDTHH:MM:SSZ)")
  end

  def call(aircraft_call_sign:, start_time:, end_time:)
    # Schema validation is handled by FastMcp, so we can proceed directly

    begin
      client = FlightLogger::Client.new

      # First find the aircraft to get its ID
      aircraft_result = client.find_aircraft(aircraft_call_sign)
      unless aircraft_result[:success] && aircraft_result[:aircraft]&.any?
        response = {
          'success' => false,
          'message' => "Aircraft '#{aircraft_call_sign}' not found",
          'conflicts' => [],
          'has_conflicts' => false
        }
        return JSON.generate(response)
      end

      aircraft = aircraft_result[:aircraft].first
      aircraft_id = aircraft['id']

      # Check conflicts using the client's expected parameters
      result = client.check_maintenance_conflicts(
        aircraft_id: aircraft_id,
        start_time: start_time,
        end_time: end_time
      )

      # Convert symbols to strings for JSON serialization
      response = {
        'success' => result[:success],
        'conflicts' => result[:conflicts],
        'has_conflicts' => result[:conflicts]&.any? || false,
        'message' => result[:message]
      }.compact

      # Return as JSON string for MCP
      JSON.generate(response)
    rescue => e
      response = {
        'success' => false,
        'message' => "Failed to check maintenance conflicts: #{e.message}",
        'conflicts' => [],
        'has_conflicts' => false
      }
      JSON.generate(response)
    end
  end
end
