# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class CheckMaintenanceConflictsTool < FastMcp::Tool
  description 'Check for maintenance scheduling conflicts for a specific aircraft and time period'

  def call(aircraft_call_sign: nil, start_time: nil, end_time: nil)
    puts "CheckMaintenanceConflictsTool: Checking conflicts for #{aircraft_call_sign} from #{start_time} to #{end_time}"

    # Validate required parameters
    if aircraft_call_sign.nil? || aircraft_call_sign.strip.empty?
      puts "CheckMaintenanceConflictsTool: Error - Aircraft call sign is required"
      return {
        success: false,
        message: "Aircraft call sign is required",
        conflicts: [],
        has_conflicts: false
      }
    end

    if start_time.nil? || start_time.strip.empty?
      puts "CheckMaintenanceConflictsTool: Error - Start time is required"
      return {
        success: false,
        message: "Start time is required (format: YYYY-MM-DDTHH:MM:SSZ)",
        conflicts: [],
        has_conflicts: false
      }
    end

    if end_time.nil? || end_time.strip.empty?
      puts "CheckMaintenanceConflictsTool: Error - End time is required"
      return {
        success: false,
        message: "End time is required (format: YYYY-MM-DDTHH:MM:SSZ)",
        conflicts: [],
        has_conflicts: false
      }
    end

    begin
      client = FlightLogger::Client.new

      # First find the aircraft to get its ID
      aircraft_result = client.find_aircraft(aircraft_call_sign)
      unless aircraft_result[:success] && aircraft_result[:aircraft]&.any?
        puts "CheckMaintenanceConflictsTool: Error - Aircraft '#{aircraft_call_sign}' not found"
        return {
          success: false,
          message: "Aircraft '#{aircraft_call_sign}' not found",
          conflicts: [],
          has_conflicts: false
        }
      end

      aircraft = aircraft_result[:aircraft].first
      aircraft_id = aircraft['id']

      puts "CheckMaintenanceConflictsTool: Found aircraft #{aircraft_call_sign} with ID #{aircraft_id}"

      # Check conflicts using the client's expected parameters
      result = client.check_maintenance_conflicts(
        aircraft_id: aircraft_id,
        start_time: start_time,
        end_time: end_time
      )

      # Add has_conflicts field for easier checking
      result[:has_conflicts] = result[:conflicts]&.any? || false

      puts "CheckMaintenanceConflictsTool: Success - Found #{result[:conflicts]&.length || 0} conflicts"
      result
    rescue => e
      puts "CheckMaintenanceConflictsTool: Error - #{e.message}"
      puts e.backtrace
      {
        success: false,
        message: "Failed to check maintenance conflicts: #{e.message}",
        conflicts: [],
        has_conflicts: false
      }
    end
  end
end
