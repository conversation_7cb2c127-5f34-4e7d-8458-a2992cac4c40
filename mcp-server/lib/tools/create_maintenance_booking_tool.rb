# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class CreateMaintenanceBookingTool < FastMcp::Tool
  description 'Create a maintenance booking for an aircraft with conflict checking'

  def call(aircraft_call_sign: nil, start_time: nil, end_time: nil, description: nil)
    puts "CreateMaintenanceBookingTool: Creating booking for #{aircraft_call_sign} from #{start_time} to #{end_time}"

    # Validate required parameters
    if aircraft_call_sign.nil? || aircraft_call_sign.strip.empty?
      puts "CreateMaintenanceBookingTool: Error - Aircraft call sign is required"
      return {
        success: false,
        message: "Aircraft call sign is required",
        booking: nil
      }
    end

    if start_time.nil? || start_time.strip.empty?
      puts "CreateMaintenanceBookingTool: Error - Start time is required"
      return {
        success: false,
        message: "Start time is required (format: YYYY-MM-DDTHH:MM:SSZ)",
        booking: nil
      }
    end

    if end_time.nil? || end_time.strip.empty?
      puts "CreateMaintenanceBookingTool: Error - End time is required"
      return {
        success: false,
        message: "End time is required (format: YYYY-MM-DDTHH:MM:SSZ)",
        booking: nil
      }
    end

    begin
      client = FlightLogger::Client.new

      # First find the aircraft to get its ID
      aircraft_result = client.find_aircraft(aircraft_call_sign)
      unless aircraft_result[:success] && aircraft_result[:aircraft]&.any?
        puts "CreateMaintenanceBookingTool: Error - Aircraft '#{aircraft_call_sign}' not found"
        return {
          success: false,
          message: "Aircraft '#{aircraft_call_sign}' not found",
          booking: nil
        }
      end

      aircraft = aircraft_result[:aircraft].first
      aircraft_id = aircraft['id']

      puts "CreateMaintenanceBookingTool: Found aircraft #{aircraft_call_sign} with ID #{aircraft_id}"

      # Create the maintenance booking using the client's expected parameters
      result = client.create_maintenance_booking(
        aircraft_id: aircraft_id,
        booking_start: start_time,
        booking_end: end_time,
        comment: description || "Maintenance booking"
      )

      puts "CreateMaintenanceBookingTool: Success - Created booking #{result[:booking]&.dig('id')}"
      result
    rescue => e
      puts "CreateMaintenanceBookingTool: Error - #{e.message}"
      puts e.backtrace
      {
        success: false,
        message: "Failed to create maintenance booking: #{e.message}",
        booking: nil
      }
    end
  end
end
