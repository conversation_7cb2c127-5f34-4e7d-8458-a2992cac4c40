# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class CreateMaintenanceBookingTool < FastMcp::Tool
  description 'Create a maintenance booking for an aircraft with conflict checking'

  def call(aircraft_call_sign: nil, start_time: nil, end_time: nil, description: nil)
    # Validate required parameters
    if aircraft_call_sign.nil? || aircraft_call_sign.strip.empty?
      response = {
        'success' => false,
        'message' => "Aircraft call sign is required",
        'booking' => nil
      }
      return JSON.generate(response)
    end

    if start_time.nil? || start_time.strip.empty?
      response = {
        'success' => false,
        'message' => "Start time is required (format: YYYY-MM-DDTHH:MM:SSZ)",
        'booking' => nil
      }
      return JSON.generate(response)
    end

    if end_time.nil? || end_time.strip.empty?
      response = {
        'success' => false,
        'message' => "End time is required (format: YYYY-MM-DDTHH:MM:SSZ)",
        'booking' => nil
      }
      return JSON.generate(response)
    end

    begin
      client = FlightLogger::Client.new

      # First find the aircraft to get its ID
      aircraft_result = client.find_aircraft(aircraft_call_sign)
      unless aircraft_result[:success] && aircraft_result[:aircraft]&.any?
        response = {
          'success' => false,
          'message' => "Aircraft '#{aircraft_call_sign}' not found",
          'booking' => nil
        }
        return JSON.generate(response)
      end

      aircraft = aircraft_result[:aircraft].first
      aircraft_id = aircraft['id']

      # Create the maintenance booking using the client's expected parameters
      result = client.create_maintenance_booking(
        aircraft_id: aircraft_id,
        booking_start: start_time,
        booking_end: end_time,
        comment: description || "Maintenance booking"
      )

      # Convert symbols to strings for JSON serialization
      response = {
        'success' => result[:success],
        'booking' => result[:booking],
        'message' => result[:message]
      }.compact

      # Return as JSON string for MCP
      JSON.generate(response)
    rescue => e
      response = {
        'success' => false,
        'message' => "Failed to create maintenance booking: #{e.message}",
        'booking' => nil
      }
      JSON.generate(response)
    end
  end
end
