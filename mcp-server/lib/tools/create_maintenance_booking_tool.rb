# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class CreateMaintenanceBookingTool < FastMcp::Tool
  description 'Create a maintenance booking for an aircraft with conflict checking'

  arguments do
    required(:aircraft_call_sign).filled(:string).description("Aircraft call sign (e.g., 'AC-A')")
    required(:start_time).filled(:string).description("Start time in ISO format (YYYY-MM-DDTHH:MM:SSZ)")
    required(:end_time).filled(:string).description("End time in ISO format (YYYY-MM-DDTHH:MM:SSZ)")
    optional(:description).filled(:string).description("Description of the maintenance work")
  end

  def call(aircraft_call_sign:, start_time:, end_time:, description: nil)
    # Schema validation is handled by FastMcp, so we can proceed directly

    begin
      client = FlightLogger::Client.new

      # First find the aircraft to get its ID
      aircraft_result = client.find_aircraft(aircraft_call_sign)
      unless aircraft_result[:success] && aircraft_result[:aircraft]&.any?
        response = {
          'success' => false,
          'message' => "Aircraft '#{aircraft_call_sign}' not found",
          'booking' => nil
        }
        return JSON.generate(response)
      end

      aircraft = aircraft_result[:aircraft].first
      aircraft_id = aircraft['id']

      # Create the maintenance booking using the client's expected parameters
      result = client.create_maintenance_booking(
        aircraft_id: aircraft_id,
        booking_start: start_time,
        booking_end: end_time,
        comment: description || "Maintenance booking"
      )

      # Convert symbols to strings for JSON serialization
      response = {
        'success' => result[:success],
        'booking' => result[:booking],
        'message' => result[:message]
      }.compact

      # Return as JSON string for MCP
      JSON.generate(response)
    rescue => e
      response = {
        'success' => false,
        'message' => "Failed to create maintenance booking: #{e.message}",
        'booking' => nil
      }
      JSON.generate(response)
    end
  end
end
