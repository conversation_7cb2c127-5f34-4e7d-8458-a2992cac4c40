# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class FindAircraftTool < FastMcp::Tool
  description 'Find a specific aircraft by name or search term'

  def call(search_term: nil)
    if search_term.nil? || search_term.strip.empty?
      response = {
        'success' => false,
        'message' => "Search term is required",
        'aircraft' => []
      }
      return JSON.generate(response)
    end

    begin
      client = FlightLogger::Client.new
      result = client.find_aircraft(search_term)

      # Convert symbols to strings for JSON serialization
      response = {
        'success' => result[:success],
        'aircraft' => result[:aircraft],
        'message' => result[:message]
      }.compact

      # Return as JSON string for MCP
      JSON.generate(response)
    rescue => e
      response = {
        'success' => false,
        'message' => "Failed to find aircraft: #{e.message}",
        'aircraft' => []
      }
      JSON.generate(response)
    end
  end
end
