# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class FindAircraftTool < FastMcp::Tool
  description 'Find a specific aircraft by name or search term'

  arguments do
    required(:search_term).filled(:string).description("Search term to find aircraft (call sign, model, etc.)")
  end

  def call(search_term:)
    # Schema validation is handled by FastMcp, so we can proceed directly

    begin
      client = FlightLogger::Client.new
      result = client.find_aircraft(search_term)

      # Convert symbols to strings for JSON serialization
      response = {
        'success' => result[:success],
        'aircraft' => result[:aircraft],
        'message' => result[:message]
      }.compact

      # Return as JSON string for MCP
      JSON.generate(response)
    rescue => e
      response = {
        'success' => false,
        'message' => "Failed to find aircraft: #{e.message}",
        'aircraft' => []
      }
      JSON.generate(response)
    end
  end
end
