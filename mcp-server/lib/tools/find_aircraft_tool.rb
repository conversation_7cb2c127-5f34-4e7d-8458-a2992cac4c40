# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class FindAircraftTool < FastMcp::Tool
  description 'Find a specific aircraft by name or search term'

  def call(search_term: nil)
    puts "FindAircraftTool: Starting search for '#{search_term}'"

    if search_term.nil? || search_term.strip.empty?
      puts "FindAircraftTool: Error - No search term provided"
      return {
        success: false,
        message: "Search term is required",
        aircraft: []
      }
    end

    begin
      client = FlightLogger::Client.new
      result = client.find_aircraft(search_term)

      puts "FindAircraftTool: Success - Found #{result[:aircraft]&.length || 0} aircraft matching '#{search_term}'"
      result
    rescue => e
      puts "FindAircraftTool: Error - #{e.message}"
      puts e.backtrace
      {
        success: false,
        message: "Failed to find aircraft: #{e.message}",
        aircraft: []
      }
    end
  end
end
