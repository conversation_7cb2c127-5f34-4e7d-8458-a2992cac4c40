# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class ListAircraftTool < FastMcp::Tool
  description 'List all aircraft in the fleet'

  def call
    begin
      client = FlightLogger::Client.new
      result = client.list_aircraft

      # Convert symbols to strings for JSON serialization
      response = {
        'success' => result[:success],
        'aircraft' => result[:aircraft],
        'message' => result[:message]
      }.compact

      # Return as JSON string for MCP
      JSON.generate(response)
    rescue => e
      response = {
        'success' => false,
        'message' => "Failed to list aircraft: #{e.message}",
        'aircraft' => []
      }
      JSON.generate(response)
    end
  end
end
