# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class ListAircraftTool < FastMcp::Tool
  description 'List all aircraft in the fleet'

  def call
    begin
      client = FlightLogger::Client.new
      result = client.list_aircraft

      # Convert symbols to strings for JSON serialization
      {
        'success' => result[:success],
        'aircraft' => result[:aircraft],
        'message' => result[:message]
      }.compact
    rescue => e
      {
        'success' => false,
        'message' => "Failed to list aircraft: #{e.message}",
        'aircraft' => []
      }
    end
  end
end
