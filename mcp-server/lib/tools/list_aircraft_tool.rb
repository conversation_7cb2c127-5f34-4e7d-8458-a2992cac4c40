# frozen_string_literal: true

require 'fast_mcp'
require_relative '../flightlogger/client'

class ListAircraftTool < FastMcp::Tool
  description 'List all aircraft in the fleet'

  def call
    puts "ListAircraftTool: Starting aircraft list request"

    begin
      client = FlightLogger::Client.new
      result = client.list_aircraft

      puts "ListAircraftTool: Success - Found #{result[:aircraft]&.length || 0} aircraft"
      result
    rescue => e
      puts "ListAircraftTool: Error - #{e.message}"
      puts e.backtrace
      {
        success: false,
        message: "Failed to list aircraft: #{e.message}",
        aircraft: []
      }
    end
  end
end
