require 'json'
require 'net/http'
require 'uri'

module FlightLogger
  class Client
    attr_reader :api_key, :base_url

    def initialize(api_key: nil, base_url: nil)
      @api_key = api_key || ENV['FLIGHTLOGGER_API_KEY']
      @base_url = base_url || ENV['FLIGHTLOGGER_BASE_URL'] || determine_base_url

      raise ConfigurationError, 'FLIGHTLOGGER_BASE_URL required' unless @base_url

      validate_credentials
    end
    def list_aircraft
      query = <<~GRAPHQL
        query {
          aircraft {
            edges {
              node {
                id
                callSign
                model
                aircraftClass
                aircraftType
                disabled
                homeAirport {
                  id
                  name
                }
                currentAirport {
                  id
                  name
                }
              }
            }
          }
        }
      GRAPHQL

      response = graphql_request(query: query)

      if response && response.dig('aircraft', 'edges')
        aircraft_list = response['aircraft']['edges'].map do |edge|
          aircraft = edge['node']
          {
            'id' => aircraft['id'],
            'callsign' => aircraft['callSign'],
            'model' => aircraft['model'],
            'aircraft_class' => aircraft['aircraftClass'],
            'aircraft_type' => aircraft['aircraftType'],
            'disabled' => aircraft['disabled'],
            'current_airport_id' => aircraft['currentAirport']&.dig('id'),
            'home_airport_id' => aircraft['homeAirport']&.dig('id'),
            'home_airport' => aircraft['homeAirport'],
            'current_airport' => aircraft['currentAirport']
          }
        end

        {
          success: true,
          aircraft: aircraft_list
        }
      else
        {
          success: false,
          message: 'No aircraft data returned'
        }
      end
    rescue => e
      {
        success: false,
        message: "Cannot connect to FlightLogger servers: #{e.message}"
      }
    end

    def find_aircraft(name_search)
      query = <<~GRAPHQL
        query {
          aircraft {
            edges {
              node {
                id
                callSign
                model
                aircraftClass
                aircraftType
                disabled
                homeAirport {
                  id
                  name
                }
                currentAirport {
                  id
                  name
                }
              }
            }
          }
        }
      GRAPHQL

      response = graphql_request(query: query)

      if response && response.dig('aircraft', 'edges')
        matching_aircraft = response['aircraft']['edges'].select do |edge|
          aircraft = edge['node']
          aircraft['callSign'].downcase.include?(name_search.downcase) ||
          aircraft['model'].downcase.include?(name_search.downcase)
        end

        transformed_aircraft = matching_aircraft.map do |edge|
          aircraft = edge['node']
          {
            'id' => aircraft['id'],
            'callsign' => aircraft['callSign'],
            'model' => aircraft['model'],
            'aircraft_class' => aircraft['aircraftClass'],
            'aircraft_type' => aircraft['aircraftType'],
            'disabled' => aircraft['disabled'],
            'current_airport_id' => aircraft['currentAirport']&.dig('id'),
            'home_airport_id' => aircraft['homeAirport']&.dig('id'),
            'home_airport' => aircraft['homeAirport'],
            'current_airport' => aircraft['currentAirport']
          }
        end

        {
          success: true,
          aircraft: transformed_aircraft
        }
      else
        {
          success: false,
          message: "Aircraft matching '#{name_search}' not found"
        }
      end
    rescue => e
      {
        success: false,
        message: "Cannot connect to FlightLogger servers: #{e.message}"
      }
    end

    def create_maintenance_booking_with_conflict_check(aircraft_id:, booking_start:, booking_end:, comment: nil, notify_via_email: nil)
      conflict_check = check_maintenance_conflicts(
        aircraft_id: aircraft_id,
        start_time: booking_start,
        end_time: booking_end
      )

      unless conflict_check[:success]
        return conflict_check
      end

      create_maintenance_booking(
        aircraft_id: aircraft_id,
        booking_start: booking_start,
        booking_end: booking_end,
        comment: comment,
        notify_via_email: notify_via_email
      )
    end

    def create_maintenance_booking(aircraft_id:, booking_start:, booking_end:, comment: nil, notify_via_email: nil)
      mutation = <<~GRAPHQL
        mutation($booking: MaintenanceBookingInput!) {
          createMaintenanceBooking(booking: $booking) {
            id
            aircraft {
              id
              callSign
            }
            startsAt
            endsAt
            comment
            status
          }
        }
      GRAPHQL

      variables = {
        booking: {
          aircraftId: aircraft_id,
          bookingStart: booking_start,
          bookingEnd: booking_end,
          comment: comment || 'Scheduled maintenance',
          notifyViaEmail: notify_via_email || false
        }
      }

      response = graphql_request(query: mutation, variables: variables)

      if response && response['createMaintenanceBooking']
        booking_data = response['createMaintenanceBooking']

        {
          success: true,
          booking: {
            'id' => booking_data['id'],
            'aircraft_id' => booking_data['aircraft']['id'],
            'aircraft_call_sign' => booking_data['aircraft']['callSign'],
            'startTime' => booking_data['startsAt'],
            'endTime' => booking_data['endsAt'],
            'comment' => booking_data['comment'],
            'status' => booking_data['status']
          }
        }
      else
        {
          success: false,
          message: 'Failed to create maintenance booking'
        }
      end
    rescue => e
      {
        success: false,
        message: "Cannot connect to FlightLogger servers: #{e.message}"
      }
    end

    def check_maintenance_conflicts(aircraft_id:, start_time:, end_time:)
      query = <<~GRAPHQL
        query($from: DateTime!, $to: DateTime!) {
          bookings(from: $from, to: $to) {
            edges {
              node {
                id
                startTime: startsAt
                endTime: endsAt
                type: typeOfBooking
                status
                ... on FlightBooking {
                  plane {
                    id
                    call_sign
                  }
                }
                ... on MaintenanceBooking {
                  plane {
                    id
                    call_sign
                  }
                }
              }
            }
          }
        }
      GRAPHQL

      variables = {
        from: start_time,
        to: end_time
      }

      response = graphql_request(query: query, variables: variables)

      if response && response['bookings'] && response['bookings']['edges']
        all_bookings = response['bookings']['edges'].map { |edge| edge['node'] }

        aircraft_bookings = all_bookings.select do |booking|
          booking['plane'] && booking['plane']['id'] == aircraft_id
        end

        conflicting_bookings = aircraft_bookings.select do |booking|
          booking_start = DateTime.parse(booking['startTime'])
          booking_end = DateTime.parse(booking['endTime'])
          request_start = DateTime.parse(start_time)
          request_end = DateTime.parse(end_time)

          (request_start < booking_end) && (request_end > booking_start)
        end

        if conflicting_bookings.any?
          conflict_details = conflicting_bookings.map do |booking|
            "#{booking['type']} booking (ID: #{booking['id']}) from #{booking['startTime']} to #{booking['endTime']}"
          end.join(', ')

          {
            success: false,
            message: "Maintenance booking conflicts with existing bookings: #{conflict_details}",
            conflicts: conflicting_bookings
          }
        else
          {
            success: true,
            message: "No conflicts found - maintenance booking can be scheduled"
          }
        end
      else
        {
          success: false,
          message: 'Failed to check for booking conflicts'
        }
      end
    rescue => e
      {
        success: false,
        message: "Cannot connect to FlightLogger servers: #{e.message}"
      }
    end

    def list_maintenance_bookings(aircraft_id: nil, from: nil, to: nil)
      query = <<~GRAPHQL
        query($from: DateTime, $to: DateTime, $subtypes: [BookingSubtypeEnum]) {
          bookings(from: $from, to: $to, subtypes: $subtypes) {
            edges {
              node {
                id
                startTime: startsAt
                endTime: endsAt
                type: typeOfBooking
                status
                comment
                ... on MaintenanceBooking {
                  plane {
                    id
                    call_sign
                  }
                }
              }
            }
          }
        }
      GRAPHQL

      variables = {
        subtypes: ['MAINTENANCE']
      }
      variables[:from] = from if from
      variables[:to] = to if to

      response = graphql_request(query: query, variables: variables)

      if response && response['bookings'] && response['bookings']['edges']
        all_bookings = response['bookings']['edges'].map { |edge| edge['node'] }

        maintenance_bookings = if aircraft_id
          all_bookings.select do |booking|
            booking['plane'] && booking['plane']['id'] == aircraft_id
          end
        else
          all_bookings
        end

        transformed_bookings = maintenance_bookings.map do |booking|
          {
            'id' => booking['id'],
            'startTime' => booking['startTime'],
            'endTime' => booking['endTime'],
            'type' => booking['type'],
            'status' => booking['status'],
            'comment' => booking['comment'],
            'plane' => booking['plane'] ? {
              'id' => booking['plane']['id'],
              'callsign' => booking['plane']['call_sign']
            } : nil
          }
        end

        {
          success: true,
          bookings: transformed_bookings
        }
      else
        {
          success: false,
          message: 'Failed to retrieve maintenance bookings'
        }
      end
    rescue => e
      {
        success: false,
        message: "Cannot connect to FlightLogger servers: #{e.message}"
      }
    end

    private

    def determine_base_url
      candidate_urls = [
        'http://api.flightlogger.test:3000',
        'http://app:3000',
        'http://localhost:3000',
        'http://host.docker.internal:3000',
        'http://**********:3000'
      ]

      puts "FlightLogger client attempting to auto-detect API endpoint..."

      candidate_urls.each do |url|
        puts "  Trying: #{url}"
        if test_connection(url)
          puts "  ✓ Success! Using: #{url}"
          return url
        else
          puts "  ✗ Failed"
        end
      end

      puts "  No working endpoint found, defaulting to api.flightlogger.test:3000"
      'http://api.flightlogger.test:3000'
    end

    def test_connection(url)
      uri = URI("#{url}/graphql")
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = uri.scheme == 'https'
      http.open_timeout = 5
      http.read_timeout = 5

      request = Net::HTTP::Post.new('/graphql')
      request['Content-Type'] = 'application/json'

      if uri.host != 'api.flightlogger.test'
        request['Host'] = 'api.flightlogger.test'
      end

      request['Authorization'] = "Bearer #{@api_key}" if @api_key

      request.body = JSON.generate({ query: '{ __schema { queryType { name } } }' })

      response = http.request(request)
      response.code == '200'
    rescue
      false
    end

    def graphql_request(query:, variables: {})
      uri = URI("#{@base_url}/graphql")
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = uri.scheme == 'https'

      request = Net::HTTP::Post.new('/graphql')
      request['Content-Type'] = 'application/json'

      if uri.host != 'api.flightlogger.test'
        request['Host'] = 'api.flightlogger.test'
      end

      request['Authorization'] = "Bearer #{@api_key}" if @api_key

      request.body = JSON.generate({ query: query, variables: variables })

      response = http.request(request)

      raise APIError, "HTTP #{response.code}: #{response.body}" unless response.code == '200'

      data = JSON.parse(response.body)

      if data['loggedOut']
        raise APIError, "Authentication required - FlightLogger API access is restricted"
      end

      if data['errors']
        error_messages = data['errors'].map { |e| e['message'] }.join(', ')
        raise GraphQLError, error_messages
      end

      data['data']
    rescue => e
      raise APIError, "API request failed: #{e.message}"
    end

    def validate_credentials
      raise ConfigurationError, 'FLIGHTLOGGER_API_KEY required' unless @api_key
    end
  end

  class APIError < StandardError; end
  class GraphQLError < StandardError; end
  class ConfigurationError < StandardError; end
end
