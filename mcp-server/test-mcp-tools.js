#!/usr/bin/env node

const { chromium } = require('playwright');

async function testMCPTools() {
  console.log('🚀 Starting MCP Tools Test...');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to the chat interface
    console.log('📱 Opening chat interface...');
    await page.goto('http://localhost:3001');

    // Check for any console errors
    page.on('console', msg => console.log('Browser console:', msg.text()));

    // Wait for the page to load
    await page.waitForTimeout(3000);

    // Take a screenshot
    await page.screenshot({ path: 'chat-interface.png' });
    console.log('📸 Screenshot saved: chat-interface.png');

    // Find the message input field and wait for it to be enabled
    console.log('🔍 Looking for message input field...');
    const messageInput = await page.locator('input[type="text"], textarea').first();
    await messageInput.waitFor({ timeout: 15000 });

    // Wait for the input to be enabled (not disabled) - check every 2 seconds for up to 60 seconds
    console.log('⏳ Waiting for input field to be enabled...');
    let attempts = 0;
    const maxAttempts = 30; // 60 seconds total

    while (attempts < maxAttempts) {
      const isDisabled = await messageInput.getAttribute('disabled');
      if (isDisabled === null) {
        console.log('✅ Input field is now enabled!');
        break;
      }

      console.log(`   Attempt ${attempts + 1}/${maxAttempts}: Input still disabled, waiting...`);
      await page.waitForTimeout(2000);
      attempts++;

      if (attempts === maxAttempts) {
        throw new Error('Input field never became enabled');
      }
    }

    console.log('✅ Chat interface loaded successfully');

    // Test 1: List Aircraft Tool
    console.log('\n🔧 Testing Tool 1: List Aircraft');
    await messageInput.fill('List all aircraft in the fleet');
    await page.keyboard.press('Enter');

    // Wait for response
    await page.waitForTimeout(5000);
    console.log('✅ List Aircraft request sent');

    // Test 2: Find Aircraft Tool
    console.log('\n🔧 Testing Tool 2: Find Aircraft');
    await messageInput.fill('Find aircraft with call sign OY-ABC');
    await page.keyboard.press('Enter');

    // Wait for response
    await page.waitForTimeout(5000);
    console.log('✅ Find Aircraft request sent');

    // Test 3: Check Maintenance Conflicts Tool
    console.log('\n🔧 Testing Tool 3: Check Maintenance Conflicts');
    await messageInput.fill('Check for maintenance conflicts for aircraft OY-ABC from 2024-01-15 10:00 to 2024-01-15 14:00');
    await page.keyboard.press('Enter');

    // Wait for response
    await page.waitForTimeout(5000);
    console.log('✅ Check Maintenance Conflicts request sent');

    // Test 4: Create Maintenance Booking Tool
    console.log('\n🔧 Testing Tool 4: Create Maintenance Booking');
    await messageInput.fill('Create a maintenance booking for aircraft OY-ABC from 2024-01-20 09:00 to 2024-01-20 17:00 for engine inspection');
    await page.keyboard.press('Enter');

    // Wait for response
    await page.waitForTimeout(8000);
    console.log('✅ Create Maintenance Booking request sent');

    // Take final screenshot
    await page.screenshot({ path: 'chat-final.png' });
    console.log('📸 Final screenshot saved: chat-final.png');

    // Get all chat messages to verify responses
    const messages = await page.locator('.message, .chat-message, [class*="message"]').allTextContents();
    console.log('\n📝 Chat Messages Found:', messages.length);

    if (messages.length > 0) {
      console.log('💬 Recent messages:');
      messages.slice(-6).forEach((msg, i) => {
        console.log(`  ${i + 1}: ${msg.substring(0, 100)}...`);
      });
    }

    console.log('\n🎉 All MCP tools tested successfully!');
    console.log('🔍 Check the screenshots and browser window to verify the responses');

  } catch (error) {
    console.error('❌ Test failed:', error);
    await page.screenshot({ path: 'error-screenshot.png' });
  } finally {
    // Keep browser open for manual inspection
    console.log('\n⏸️  Browser will remain open for manual inspection...');
    console.log('   Press Ctrl+C to close when done');

    // Wait indefinitely until user closes
    await new Promise(() => {});
  }
}

// Run the test
testMCPTools().catch(console.error);
