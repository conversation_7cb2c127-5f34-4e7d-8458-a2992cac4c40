#!/usr/bin/env node

const http = require('http');
const { spawn } = require('child_process');
const { URL } = require('url');

class MCPBridge {
  constructor() {
    this.mcpProcess = null;
    this.requestQueue = [];
    this.isReady = false;
    this.startMCPProcess();
  }

  startMCPProcess() {
    console.log('Starting MCP STDIO process...');
    
    this.mcpProcess = spawn('ruby', ['bin/server_stdio'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: '/app'
    });

    this.mcpProcess.stdout.on('data', (data) => {
      const lines = data.toString().split('\n').filter(line => line.trim());
      
      lines.forEach(line => {
        try {
          const response = JSON.parse(line);
          this.handleMCPResponse(response);
        } catch (e) {
          // Not JSON, probably a log message
          console.log('MCP Log:', line);
          if (line.includes('Starting FlightLogger MCP Server')) {
            this.isReady = true;
            console.log('MCP Server is ready!');
          }
        }
      });
    });

    this.mcpProcess.stderr.on('data', (data) => {
      console.error('MCP Error:', data.toString());
    });

    this.mcpProcess.on('close', (code) => {
      console.log(`MCP process exited with code ${code}`);
      this.isReady = false;
    });

    // Give the process time to start
    setTimeout(() => {
      if (!this.isReady) {
        this.isReady = true;
        console.log('Assuming MCP Server is ready after timeout');
      }
    }, 3000);
  }

  handleMCPResponse(response) {
    // Find the pending request with matching ID
    const requestIndex = this.requestQueue.findIndex(req => req.id === response.id);
    if (requestIndex !== -1) {
      const request = this.requestQueue.splice(requestIndex, 1)[0];
      request.resolve(response);
    }
  }

  async sendMCPRequest(request) {
    return new Promise((resolve, reject) => {
      if (!this.isReady || !this.mcpProcess) {
        reject(new Error('MCP server not ready'));
        return;
      }

      // Add to queue
      this.requestQueue.push({ id: request.id, resolve, reject });

      // Send to MCP process
      const requestStr = JSON.stringify(request) + '\n';
      this.mcpProcess.stdin.write(requestStr);

      // Timeout after 30 seconds
      setTimeout(() => {
        const index = this.requestQueue.findIndex(req => req.id === request.id);
        if (index !== -1) {
          this.requestQueue.splice(index, 1);
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }
}

// Create bridge instance
const bridge = new MCPBridge();

// Create HTTP server
const server = http.createServer(async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = new URL(req.url, `http://${req.headers.host}`);

  // Health check endpoint
  if (url.pathname === '/health') {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('OK');
    return;
  }

  // MCP messages endpoint
  if (url.pathname === '/messages' && req.method === 'POST') {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const request = JSON.parse(body);
        
        // Ensure request has an ID
        if (!request.id) {
          request.id = Date.now();
        }

        const response = await bridge.sendMCPRequest(request);
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(response));
      } catch (error) {
        console.error('Request error:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          jsonrpc: '2.0',
          id: null,
          error: {
            code: -32603,
            message: error.message
          }
        }));
      }
    });
    return;
  }

  // Default response
  res.writeHead(200, { 'Content-Type': 'text/plain' });
  res.end('FlightLogger MCP Bridge Server');
});

const port = process.env.PORT || 8080;
server.listen(port, '0.0.0.0', () => {
  console.log(`MCP Bridge Server listening on port ${port}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down...');
  if (bridge.mcpProcess) {
    bridge.mcpProcess.kill();
  }
  server.close();
});
