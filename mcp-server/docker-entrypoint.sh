#!/bin/sh

# Resolve the 'app' container IP and add hosts entries for subdomains
APP_IP=$(getent hosts app | awk '{ print $1 }')

if [ -n "$APP_IP" ]; then
    echo "Adding FlightLogger subdomain entries to /etc/hosts for IP: $APP_IP"
    echo "$APP_IP api.flightlogger.test" >> /etc/hosts
    echo "$APP_IP demo.flightlogger.test" >> /etc/hosts
    echo "$APP_IP flightlogger.test" >> /etc/hosts
else
    echo "Warning: Could not resolve 'app' container IP"
fi

# Start the MCP bridge server (HTTP-to-STDIO bridge)
exec node bridge-server.js
