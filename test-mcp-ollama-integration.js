const { chromium } = require('playwright');

async function testMCPOllamaIntegration() {
  console.log('🚀 Testing MCP-Ollama Integration...');

  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();

  // Listen to console messages
  page.on('console', msg => {
    console.log(`🖥️  Console [${msg.type()}]:`, msg.text());
  });

  // Listen to network requests
  page.on('response', response => {
    if (response.url().includes('/mcp/') || response.url().includes('/ollama/')) {
      console.log(`🌐 Network: ${response.status()} ${response.url()}`);
    }
  });

  try {
    console.log('📱 Navigating to chat app...');
    await page.goto('http://localhost:3001');

    console.log('⏳ Waiting for chat interface...');
    await page.waitForSelector('.status-indicator', { timeout: 10000 });

    const connectionStatus = await page.textContent('.status-indicator');
    console.log(`🔗 Connection status: ${connectionStatus}`);

    console.log('💬 Sending test message...');
    await page.fill('.message-input', 'List all aircraft');
    await page.click('.send-button');

    console.log('📤 Message sent, waiting for response...');

    // Wait for thinking message to appear
    await page.waitForSelector('text=Thinking...', { timeout: 5000 });
    console.log('🤔 "Thinking..." message appeared');

    // Wait for response (up to 60 seconds)
    let attempts = 0;
    const maxAttempts = 60;

    while (attempts < maxAttempts) {
      await page.waitForTimeout(1000);
      attempts++;

      const thinkingVisible = await page.isVisible('text=Thinking...');
      const messages = await page.$$('.message');
      const aiMessages = await page.$$('.ai-message');
      const errorMessages = await page.$$('.system-message');

      console.log(`⏱️  Attempt ${attempts}/${maxAttempts}: Thinking visible: ${thinkingVisible}, Total messages: ${messages.length}, AI messages: ${aiMessages.length}, Error messages: ${errorMessages.length}`);

      if (!thinkingVisible && (aiMessages.length > 0 || errorMessages.length > 0)) {
        console.log('✅ Response received!');

        // Get all messages to see what happened
        const allMessages = await page.$$eval('.message', messages =>
          messages.map(msg => ({
            type: msg.className,
            content: msg.querySelector('.message-content')?.textContent || ''
          }))
        );

        console.log('📝 All messages:');
        allMessages.forEach((msg, i) => {
          console.log(`  ${i + 1}. [${msg.type}] ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`);
        });

        break;
      }
    }

    if (attempts >= maxAttempts) {
      console.log('⏰ Timeout waiting for response');
    }

    console.log('📸 Taking screenshot...');
    await page.screenshot({ path: 'mcp-ollama-test-result.png', fullPage: true });

  } catch (error) {
    console.error('❌ Test failed:', error);
    await page.screenshot({ path: 'mcp-ollama-test-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

testMCPOllamaIntegration();
