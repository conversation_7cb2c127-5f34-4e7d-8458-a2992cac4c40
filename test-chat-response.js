const { chromium } = require('playwright');

async function testChatResponse() {
  console.log('🚀 Starting Chat Response Test...');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navigate to chat app
    console.log('📱 Navigating to chat app...');
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');

    // Wait for the chat interface to load
    console.log('⏳ Waiting for chat interface...');
    await page.waitForSelector('.chat-container', { timeout: 10000 });

    // Check if connection status shows connected
    const connectionStatus = await page.textContent('.status-indicator');
    console.log(`🔗 Connection status: ${connectionStatus}`);

    // Find the message input and send a simple message
    console.log('💬 Sending test message...');
    const messageInput = await page.locator('.message-input').first();

    if (await messageInput.count() === 0) {
      console.log('❌ Could not find message input field');
      return;
    }

    await messageInput.fill('Hello, can you help me?');

    // Find and click send button
    const sendButton = await page.locator('.send-button').first();
    if (await sendButton.count() > 0) {
      await sendButton.click();
    } else {
      // Try pressing Enter
      await messageInput.press('Enter');
    }

    console.log('📤 Message sent, waiting for response...');

    // Wait for the "Thinking..." message to appear
    try {
      await page.waitForSelector(':text("Thinking")', { timeout: 5000 });
      console.log('🤔 "Thinking..." message appeared');
    } catch (e) {
      console.log('⚠️  No "Thinking..." message found');
    }

    // Wait for a response (either AI message or error)
    console.log('⏳ Waiting for AI response...');

    // Wait up to 30 seconds for a response
    let responseReceived = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds

    while (!responseReceived && attempts < maxAttempts) {
      await page.waitForTimeout(1000); // Wait 1 second
      attempts++;

      // Check if "Thinking..." is still visible
      const thinkingVisible = await page.locator(':text("Thinking")').count() > 0;

      // Check for AI messages
      const aiMessages = await page.locator('.ai-message, .message.ai-message').count();

      // Check for error messages
      const errorMessages = await page.locator('.system-message, .message.system-message, :text("Error")').count();

      console.log(`⏱️  Attempt ${attempts}/30: Thinking visible: ${thinkingVisible}, AI messages: ${aiMessages}, Error messages: ${errorMessages}`);

      if (!thinkingVisible && (aiMessages > 0 || errorMessages > 0)) {
        responseReceived = true;
        console.log('✅ Response received!');

        // Get the latest message content
        const messages = await page.locator('.message').all();
        if (messages.length > 0) {
          const lastMessage = messages[messages.length - 1];
          const messageText = await lastMessage.textContent();
          console.log(`📝 Last message: ${messageText?.substring(0, 200)}...`);
        }
      }
    }

    if (!responseReceived) {
      console.log('❌ No response received within 30 seconds');
      console.log('🔍 Current page state:');

      // Debug: Check what messages are currently visible
      const allMessages = await page.locator('.message').all();
      console.log(`📊 Total messages visible: ${allMessages.length}`);

      for (let i = 0; i < allMessages.length; i++) {
        const messageText = await allMessages[i].textContent();
        const messageClass = await allMessages[i].getAttribute('class');
        console.log(`  Message ${i + 1}: [${messageClass}] ${messageText?.substring(0, 100)}...`);
      }

      // Check if still thinking
      const stillThinking = await page.locator(':text("Thinking")').count() > 0;
      console.log(`🤔 Still showing "Thinking...": ${stillThinking}`);
    }

    // Take a screenshot for debugging
    await page.screenshot({ path: 'chat-test-result.png', fullPage: true });
    console.log('📸 Screenshot saved as chat-test-result.png');

  } catch (error) {
    console.error('❌ Test failed:', error);
    await page.screenshot({ path: 'chat-test-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

// Run the test
testChatResponse().catch(console.error);
