import { MCPResponse, MCPToolResponse, OllamaResponse } from '../types';

const MCP_URL = '/mcp/messages';
const OLLAMA_URL = '/ollama/api/generate';

export class MCPService {
  static async listTools(): Promise<any[]> {
    try {
      const response = await fetch(MCP_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'tools/list',
          params: {},
        }),
      });

      const data: MCPResponse<{ tools: any[] }> = await response.json();
      return data.result?.tools || [];
    } catch (error) {
      console.error('MCP tools list failed:', error);
      return [];
    }
  }

  static async callTool(toolName: string, args: Record<string, any> = {}): Promise<MCPToolResponse> {
    try {
      const response = await fetch(MCP_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: args,
          },
        }),
      });

      const data: MCPResponse<MCPToolResponse> = await response.json();
      if (data.result) return data.result;
      if (data.error) return { success: false, message: data.error.message };
      return { success: false, message: 'Unknown error' };
    } catch (error) {
      console.error('MCP call failed:', error);
      throw error;
    }
  }

  static formatToolsForLLM(tools: any[]): string {
    if (!tools.length) return 'No tools available.';

    return tools.map((tool) => `- **${tool.name}**: ${tool.description}`).join('\n');
  }
}

export class OllamaService {
  private static conversationContext: number[] = [];

  static async generate(prompt: string): Promise<string> {
    try {
      const response = await fetch(OLLAMA_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: 'llama3.2:1b',
          prompt: prompt,
          stream: false,
          context: this.conversationContext,
        }),
      });

      const data: OllamaResponse = await response.json();
      this.conversationContext = data.context || [];
      return data.response;
    } catch (error) {
      console.error('Ollama call failed:', error);
      throw error;
    }
  }

  static resetContext(): void {
    this.conversationContext = [];
  }
}
