import { MCPResponse, MCPToolResponse, OllamaResponse } from '../types';

const MCP_URL = '/mcp/messages';
const OLLAMA_URL = '/ollama/api/generate';

export class MCPService {
  static async listTools(): Promise<any[]> {
    try {
      const response = await fetch(MCP_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'tools/list',
          params: {},
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const text = await response.text();
      if (!text.trim()) {
        throw new Error('Empty response from MCP server');
      }

      const data: MCPResponse<{ tools: any[] }> = JSON.parse(text);
      return data.result?.tools || [];
    } catch (error) {
      console.error('MCP tools list failed:', error);
      return [];
    }
  }

  static async callTool(toolName: string, args: Record<string, any> = {}): Promise<MCPToolResponse> {
    try {
      const response = await fetch(MCP_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: args,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const text = await response.text();
      if (!text.trim()) {
        throw new Error('Empty response from MCP server');
      }

      const data: MCPResponse<any> = JSON.parse(text);

      if (data.result && data.result.content && data.result.content[0]) {
        // Parse the JSON content from the MCP response
        const toolResult = JSON.parse(data.result.content[0].text);
        return toolResult;
      }

      if (data.error) return { success: false, message: data.error.message };
      return { success: false, message: 'Unknown error' };
    } catch (error) {
      console.error('MCP call failed:', error);
      throw error;
    }
  }

  static formatToolsForLLM(tools: any[]): string {
    if (!tools.length) return 'No tools available.';

    return tools.map((tool) => {
      const schema = tool.inputSchema || {};
      const properties = schema.properties || {};
      const required = schema.required || [];

      let argsDescription = '';
      if (Object.keys(properties).length > 0) {
        const argsList = Object.entries(properties).map(([key, value]: [string, any]) => {
          const isRequired = required.includes(key);
          return `${key}${isRequired ? ' (required)' : ' (optional)'}: ${value.type || 'any'}`;
        }).join(', ');
        argsDescription = ` - Arguments: {${argsList}}`;
      } else {
        argsDescription = ' - Arguments: {} (no arguments needed)';
      }

      return `- **${tool.name}**: ${tool.description}${argsDescription}`;
    }).join('\n');
  }
}

export class OllamaService {
  private static conversationContext: number[] = [];

  static async generate(prompt: string): Promise<string> {
    try {
      const response = await fetch(OLLAMA_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: 'llama3.2:1b',
          prompt: prompt,
          stream: false,
          context: this.conversationContext,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const text = await response.text();
      if (!text.trim()) {
        throw new Error('Empty response from Ollama server');
      }

      const data: OllamaResponse = JSON.parse(text);
      this.conversationContext = data.context || [];
      return data.response;
    } catch (error) {
      console.error('Ollama call failed:', error);
      throw error;
    }
  }

  static resetContext(): void {
    this.conversationContext = [];
  }
}
