import React from 'react';
import { Message as MessageType } from '../types';

interface MessageProps {
  message: MessageType;
}


const renderSimpleMarkdown = (text: string) => {
  const lines = text.split('\n');
  const elements: React.ReactNode[] = [];
  let inCodeBlock = false;
  let codeBlockContent: string[] = [];
  let inList = false;
  let listItems: React.ReactNode[] = [];

  const processInlineFormatting = (text: string) => {
    // Handle code spans first
    text = text.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Handle bold
    text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

    // Handle italic
    text = text.replace(/\*([^*]+)\*/g, '<em>$1</em>');

    // Handle links
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

    return text;
  };

  const parseInlineHTML = (htmlString: string) => {
    const parts = htmlString.split(/(<[^>]+>.*?<\/[^>]+>|<[^>]+\/>)/);
    return parts.map((part, i) => {
      if (part.startsWith('<code>')) {
        return <code key={i} className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono">{part.slice(6, -7)}</code>;
      } else if (part.startsWith('<strong>')) {
        return <strong key={i}>{part.slice(8, -9)}</strong>;
      } else if (part.startsWith('<em>')) {
        return <em key={i}>{part.slice(4, -5)}</em>;
      } else if (part.startsWith('<a ')) {
        const href = part.match(/href="([^"]+)"/)?.[1] || '';
        const text = part.match(/>([^<]+)</)?.[1] || '';
        return <a key={i} href={href} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 underline hover:text-blue-800 dark:hover:text-blue-300">{text}</a>;
      }
      return part;
    });
  };

  const flushList = () => {
    if (listItems.length > 0) {
      elements.push(
        <ul key={`list-${elements.length}`} className="list-disc list-inside mb-3 space-y-1">
          {listItems}
        </ul>
      );
      listItems = [];
      inList = false;
    }
  };

  lines.forEach((line, index) => {
    // Handle code blocks
    if (line.startsWith('```')) {
      if (inCodeBlock) {
        // End code block
        elements.push(
          <pre key={index} className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg overflow-x-auto mb-3">
            <code className="text-sm font-mono">{codeBlockContent.join('\n')}</code>
          </pre>
        );
        codeBlockContent = [];
        inCodeBlock = false;
      } else {
        // Start code block
        flushList();
        inCodeBlock = true;
      }
      return;
    }

    if (inCodeBlock) {
      codeBlockContent.push(line);
      return;
    }

    // Handle headings
    if (line.startsWith('### ')) {
      flushList();
      elements.push(
        <h3 key={index} className="text-lg font-semibold mb-2 mt-4">
          {parseInlineHTML(processInlineFormatting(line.slice(4)))}
        </h3>
      );
    } else if (line.startsWith('## ')) {
      flushList();
      elements.push(
        <h2 key={index} className="text-xl font-semibold mb-3 mt-4">
          {parseInlineHTML(processInlineFormatting(line.slice(3)))}
        </h2>
      );
    } else if (line.startsWith('# ')) {
      flushList();
      elements.push(
        <h1 key={index} className="text-2xl font-bold mb-3 mt-4">
          {parseInlineHTML(processInlineFormatting(line.slice(2)))}
        </h1>
      );
    } else if (line.startsWith('- ') || line.startsWith('* ') || /^\d+\.\s/.test(line)) {
      // Handle list items
      const content = line.replace(/^[-*]\s|^\d+\.\s/, '');
      listItems.push(
        <li key={`${index}-${listItems.length}`} className="ml-4">
          {parseInlineHTML(processInlineFormatting(content))}
        </li>
      );
      inList = true;
    } else if (line.trim() === '') {
      // Empty line - flush list if we're in one
      if (inList) {
        flushList();
      }
      if (!inList && elements.length > 0) {
        elements.push(<div key={index} className="mb-2"></div>);
      }
    } else {
      // Regular paragraph
      flushList();
      if (line.trim()) {
        elements.push(
          <p key={index} className="mb-2 leading-relaxed">
            {parseInlineHTML(processInlineFormatting(line))}
          </p>
        );
      }
    }
  });

  // Flush any remaining list
  flushList();

  return <div className="markdown-content">{elements}</div>;
};

export const Message: React.FC<MessageProps> = ({ message }) => {
  const renderContent = () => {
    if (message.isSystem) {
      return <span>{message.content}</span>;
    }

    if (message.type === 'ai') {
      return renderSimpleMarkdown(message.content);
    }

    return <span>{message.content}</span>;
  };

  const formatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  if (message.isSystem) {
    return (
      <div className="message system">
        <div className="message-content">
          {renderContent()}
        </div>
      </div>
    );
  }

  if (message.type === 'user') {
    return (
      <div className="message user">
        <div className="message-content">
          {renderContent()}
        </div>
        <div className="message-time">
          {formatTime(message.timestamp)}
        </div>
      </div>
    );
  }

  return (
    <div className="message assistant">
      <div className="message-content">
        {renderContent()}
      </div>
      <div className="message-time">
        {formatTime(message.timestamp)}
      </div>
    </div>
  );
};
