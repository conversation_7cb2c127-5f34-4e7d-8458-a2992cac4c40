import React from 'react';
import { Message as MessageType } from '../types';

interface MessageProps {
  message: MessageType;
}


// Import ReactMarkdown dynamically to avoid build issues
const ReactMarkdown = React.lazy(() => import('react-markdown'));

const renderMarkdown = (text: string) => {
  return (
    <div className="markdown-content">
      <React.Suspense fallback={<div>Loading...</div>}>
        <ReactMarkdown
          components={{
          h1: ({ children }) => <h1 className="text-2xl font-bold mb-3 mt-4 text-gray-900 dark:text-gray-100">{children}</h1>,
          h2: ({ children }) => <h2 className="text-xl font-semibold mb-3 mt-4 text-gray-900 dark:text-gray-100">{children}</h2>,
          h3: ({ children }) => <h3 className="text-lg font-semibold mb-2 mt-4 text-gray-900 dark:text-gray-100">{children}</h3>,
          p: ({ children }) => <p className="mb-2 leading-relaxed text-gray-900 dark:text-gray-100">{children}</p>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-3 space-y-1 text-gray-900 dark:text-gray-100">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-3 space-y-1 text-gray-900 dark:text-gray-100">{children}</ol>,
          li: ({ children }) => <li className="ml-4 text-gray-900 dark:text-gray-100">{children}</li>,
          code: ({ children, className }) => {
            const isInline = !className;
            if (isInline) {
              return <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-900 dark:text-gray-100">{children}</code>;
            }
            return <code className="text-sm font-mono text-gray-900 dark:text-gray-100">{children}</code>;
          },
          pre: ({ children }) => (
            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg overflow-x-auto mb-3 text-gray-900 dark:text-gray-100">
              {children}
            </pre>
          ),
          a: ({ href, children }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 underline hover:text-blue-800 dark:hover:text-blue-300"
            >
              {children}
            </a>
          ),
          strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-gray-100">{children}</strong>,
          em: ({ children }) => <em className="italic text-gray-900 dark:text-gray-100">{children}</em>,
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic mb-3 text-gray-700 dark:text-gray-300">
              {children}
            </blockquote>
          ),
        }}
      >
        {text}
      </ReactMarkdown>
    </React.Suspense>
    </div>
  );
};

export const Message: React.FC<MessageProps> = ({ message }) => {
  const renderContent = () => {
    if (message.isSystem) {
      return <span>{message.content}</span>;
    }

    if (message.type === 'ai') {
      return renderMarkdown(message.content);
    }

    return <span>{message.content}</span>;
  };

  const formatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  if (message.isSystem) {
    return (
      <div className="message system">
        <div className="message-content">
          {renderContent()}
        </div>
      </div>
    );
  }

  if (message.type === 'user') {
    return (
      <div className="message user">
        <div className="message-content">
          {renderContent()}
        </div>
        <div className="message-time">
          {formatTime(message.timestamp)}
        </div>
      </div>
    );
  }

  return (
    <div className="message assistant">
      <div className="message-content">
        {renderContent()}
      </div>
      <div className="message-time">
        {formatTime(message.timestamp)}
      </div>
    </div>
  );
};
