import React from 'react';
import { Message as MessageType } from '../types';

interface MessageProps {
  message: MessageType;
}

// Simple markdown renderer for basic formatting
const renderSimpleMarkdown = (text: string) => {
  const lines = text.split('\n');
  const elements: React.ReactNode[] = [];

  lines.forEach((line, index) => {
    if (line.startsWith('# ')) {
      elements.push(
        <h1 key={index} className="text-xl font-bold mb-2">
          {line.slice(2)}
        </h1>,
      );
    } else if (line.startsWith('## ')) {
      elements.push(
        <h2 key={index} className="text-lg font-bold mb-2">
          {line.slice(3)}
        </h2>,
      );
    } else if (line.startsWith('- ')) {
      elements.push(
        <li key={index} className="ml-4">
          {line.slice(2)}
        </li>,
      );
    } else if (line.includes('**') && line.includes('**')) {
      const parts = line.split('**');
      const formatted = parts.map((part, i) => (i % 2 === 1 ? <strong key={i}>{part}</strong> : part));
      elements.push(
        <p key={index} className="mb-1">
          {formatted}
        </p>,
      );
    } else if (line.includes('*') && line.includes('*')) {
      const parts = line.split('*');
      const formatted = parts.map((part, i) => (i % 2 === 1 ? <em key={i}>{part}</em> : part));
      elements.push(
        <p key={index} className="mb-1">
          {formatted}
        </p>,
      );
    } else if (line.trim() === '') {
      elements.push(<br key={index} />);
    } else {
      elements.push(
        <p key={index} className="mb-1">
          {line}
        </p>,
      );
    }
  });

  return <div>{elements}</div>;
};

export const Message: React.FC<MessageProps> = ({ message }) => {
  const renderContent = () => {
    // For system messages, render as plain text
    if (message.isSystem) {
      return <span>{message.content}</span>;
    }

    // For AI messages, render with simple markdown
    if (message.type === 'ai') {
      return renderSimpleMarkdown(message.content);
    }

    // For user messages, render as plain text
    return <span>{message.content}</span>;
  };

  const formatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  if (message.isSystem) {
    return (
      <div className="flex justify-center">
        <div className="max-w-md px-4 py-2 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg text-sm text-center border border-blue-200 dark:border-blue-700 opacity-90">
          {renderContent()}
        </div>
      </div>
    );
  }

  if (message.type === 'user') {
    return (
      <div className="flex justify-end">
        <div className="max-w-xs lg:max-w-md">
          <div className="px-6 py-4 bg-blue-500 text-white rounded-2xl rounded-br-md shadow-lg">
            {renderContent()}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-start">
      <div className="max-w-xs lg:max-w-md">
        <div className="px-6 py-4 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-2xl rounded-bl-md shadow-lg">
          {renderContent()}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {formatTime(message.timestamp)}
        </div>
      </div>
    </div>
  );
};
