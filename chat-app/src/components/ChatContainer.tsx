import React from 'react';
import { Message as MessageType } from '../types';
import { ChatInput } from './ChatInput';
import { Message } from './Message';
import { ToolsBar } from './ToolsBar';



interface ChatContainerProps {
  messages: MessageType[];
  isLoading: boolean;
  isConnected: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  onSendMessage: (message: string) => void;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  isConnected,
  messagesEndRef,
  onSendMessage,
}: ChatContainerProps) => {
  const [dynamicTitle, setDynamicTitle] = React.useState<string>('New Chat');
  const [isGeneratingTitle, setIsGeneratingTitle] = React.useState<boolean>(false);

  const generateLLMTitle = async (conversationContext: string): Promise<string> => {
    try {
      setIsGeneratingTitle(true);

      // Import OllamaService dynamically to avoid circular imports
      const { OllamaService } = await import('../services/api');

      // Create a prompt for generating a concise title
      const prompt = `Based on this conversation, create a concise description in maximum 7 words that captures the main topic or purpose. Be specific and descriptive but brief.

Conversation context:
${conversationContext}

Respond with only the title, no quotes or extra text:`;

      const response = await OllamaService.generate(prompt);

      // Clean up the response
      const cleanTitle = response.trim().replace(/['"]/g, '').substring(0, 50);
      return cleanTitle || 'Chat Discussion';
    } catch (error) {
      console.error('Error generating title:', error);
      return 'Chat Discussion';
    } finally {
      setIsGeneratingTitle(false);
    }
  };

  const generateFallbackTitle = (firstMessage: string): string => {
    // Take first 50 characters and clean it up
    const cleaned = firstMessage.trim().replace(/\n/g, ' ');
    if (cleaned.length <= 50) return cleaned;

    // Find a good breaking point (space, punctuation)
    const truncated = cleaned.substring(0, 50);
    const lastSpace = truncated.lastIndexOf(' ');
    const lastPunct = Math.max(truncated.lastIndexOf('.'), truncated.lastIndexOf('!'), truncated.lastIndexOf('?'));

    if (lastPunct > 30) return truncated.substring(0, lastPunct + 1);
    if (lastSpace > 30) return truncated.substring(0, lastSpace) + '...';
    return truncated + '...';
  };

  // Update title when messages change
  React.useEffect(() => {
    const updateTitle = async () => {
      if (messages.length === 0) {
        setDynamicTitle('New Chat');
        return;
      }

      // Get conversation context (first few messages)
      const contextMessages = messages.slice(0, 4); // First 4 messages for context
      const conversationText = contextMessages
        .map(msg => `${msg.type}: ${msg.content}`)
        .join('\n');

      // If we have enough conversation, generate LLM title
      if (messages.length >= 2 && conversationText.length > 50) {
        const llmTitle = await generateLLMTitle(conversationText);
        setDynamicTitle(llmTitle);
      } else if (messages.length > 0) {
        // Fallback to simple title generation
        const firstUserMessage = messages.find(msg => msg.type === 'user');
        if (firstUserMessage) {
          setDynamicTitle(generateFallbackTitle(firstUserMessage.content));
        }
      }
    };

    updateTitle();
  }, [messages]);

  const getChatTitle = (): string => {
    if (isGeneratingTitle) {
      return 'Generating title...';
    }
    return dynamicTitle;
  };

  const handleShowAllTools = () => {
    // TODO: Open tools modal/sidebar
    console.log('Show all tools');
  };
  return (
    <div className="chat-container">
      <div className="chat-window">
        <ToolsBar
          chatTitle={getChatTitle()}
          onShowAllTools={handleShowAllTools}
        />
        <div className="messages-container">
          {messages.map((message) => (
            <Message key={message.id} message={message} />
          ))}
          {isLoading && (
            <div className="message assistant">
              <div className="message-content">
                <div className="loading-spinner"></div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        <div className="chat-input-container">
          <ChatInput onSendMessage={onSendMessage} isConnected={isConnected} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
};
