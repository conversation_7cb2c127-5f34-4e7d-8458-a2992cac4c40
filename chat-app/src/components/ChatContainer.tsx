import React from 'react';
import { Message as MessageType } from '../types';
import { ChatInput } from './ChatInput';
import { Message } from './Message';

interface ChatContainerProps {
  messages: MessageType[];
  isLoading: boolean;
  isConnected: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  onSendMessage: (message: string) => void;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  isConnected,
  messagesEndRef,
  onSendMessage,
}: ChatContainerProps) => {
  return (
    <div className="chat-container">
      <div className="chat-window">
        <div className="messages-container">
          {messages.map((message) => (
            <Message key={message.id} message={message} />
          ))}
          {isLoading && (
            <div className="message assistant">
              <div className="message-content">
                <div className="loading-spinner"></div>
                <span>Thinking...</span>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        <div className="chat-input-container">
          <ChatInput onSendMessage={onSendMessage} isConnected={isConnected} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
};
