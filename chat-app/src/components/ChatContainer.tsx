import React from 'react';
import { Message as MessageType } from '../types';
import { ChatInput } from './ChatInput';
import { Message } from './Message';
import { ToolsBar } from './ToolsBar';



interface ChatContainerProps {
  messages: MessageType[];
  isLoading: boolean;
  isConnected: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  onSendMessage: (message: string) => void;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  isConnected,
  messagesEndRef,
  onSendMessage,
}: ChatContainerProps) => {
  const generateChatTitle = (firstMessage: string): string => {
    // Take first 50 characters and clean it up
    const cleaned = firstMessage.trim().replace(/\n/g, ' ');
    if (cleaned.length <= 50) return cleaned;

    // Find a good breaking point (space, punctuation)
    const truncated = cleaned.substring(0, 50);
    const lastSpace = truncated.lastIndexOf(' ');
    const lastPunct = Math.max(truncated.lastIndexOf('.'), truncated.lastIndexOf('!'), truncated.lastIndexOf('?'));

    if (lastPunct > 30) return truncated.substring(0, lastPunct + 1);
    if (lastSpace > 30) return truncated.substring(0, lastSpace) + '...';
    return truncated + '...';
  };

  const getChatTitle = (): string => {
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      return generateChatTitle(firstUserMessage.content);
    }
    return 'New Chat';
  };

  const handleShowAllTools = () => {
    // TODO: Open tools modal/sidebar
    console.log('Show all tools');
  };
  return (
    <div className="chat-container">
      <div className="chat-window">
        <ToolsBar
          chatTitle={getChatTitle()}
          onShowAllTools={handleShowAllTools}
        />
        <div className="messages-container">
          {messages.map((message) => (
            <Message key={message.id} message={message} />
          ))}
          {isLoading && (
            <div className="message assistant">
              <div className="message-content">
                <div className="loading-spinner"></div>
                <span>Thinking...</span>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        <div className="chat-input-container">
          <ChatInput onSendMessage={onSendMessage} isConnected={isConnected} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
};
