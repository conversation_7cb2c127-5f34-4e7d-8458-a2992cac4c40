import React from 'react';
import { Message as MessageType } from '../types';
import { ChatInput } from './ChatInput';
import { Message } from './Message';

interface ChatContainerProps {
  messages: MessageType[];
  isLoading: boolean;
  isConnected: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  onSendMessage: (message: string) => void;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  isConnected,
  messagesEndRef,
  onSendMessage,
}: ChatContainerProps) => {
  return (
    <div className="col-span-9 flex flex-col bg-flightlogger-blue dark:bg-gray-700 p-8">
      {/* Chat Window - Smaller and Centered */}
      <div className="flex-1 flex flex-col max-w-4xl mx-auto w-full bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-600">
        {/* Messages Area */}
        <div className="flex-1 p-8 overflow-y-auto space-y-6 max-h-[calc(100vh-200px)]">
          {messages.map((message) => (
            <Message key={message.id} message={message} />
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="max-w-xs lg:max-w-md px-6 py-4 bg-gray-100 dark:bg-gray-700 rounded-2xl rounded-bl-md shadow-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-gray-700 dark:text-gray-300">Thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800">
          <ChatInput onSendMessage={onSendMessage} isConnected={isConnected} isLoading={isLoading} />
        </div>
      </div>
    </div>
  );
};
