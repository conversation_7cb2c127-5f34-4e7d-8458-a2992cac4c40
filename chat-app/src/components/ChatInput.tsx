import React, { useState } from 'react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isConnected: boolean;
  isLoading: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({ onSendMessage, isConnected, isLoading }) => {
  const [inputValue, setInputValue] = useState('');

  const handleSend = () => {
    if (!inputValue.trim() || isLoading) return;
    onSendMessage(inputValue.trim());
    setInputValue('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <form className="chat-input-form" onSubmit={(e) => { e.preventDefault(); handleSend(); }}>
      <input
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Ask me about your aircraft, maintenance, or flight operations..."
        disabled={!isConnected || isLoading}
        className="chat-input"
      />
      <button
        type="submit"
        disabled={!isConnected || isLoading || !inputValue.trim()}
        className="send-btn"
      >
        Send
      </button>
    </form>
  );
};
