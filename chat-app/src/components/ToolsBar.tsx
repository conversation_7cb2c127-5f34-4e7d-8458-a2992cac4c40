import React from 'react';



interface ToolsBarProps {
  chatTitle: string;
  onShowAllTools: () => void;
}

export const ToolsBar: React.FC<ToolsBarProps> = ({
  chatTitle,
  onShowAllTools
}) => {
  return (
    <div className="tools-bar">
      <div className="chat-title-section">
        <h2 className="chat-title">{chatTitle}</h2>
      </div>
      <button className="tools-menu-btn" onClick={onShowAllTools}>
        <span className="tools-menu-icon">⚙</span>
        <span className="tools-menu-text">Tools</span>
      </button>
    </div>
  );
};
