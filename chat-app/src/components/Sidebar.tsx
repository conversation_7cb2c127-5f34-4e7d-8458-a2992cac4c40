import React, { useEffect, useState } from 'react';
import { ChatSession } from '../types';

interface SidebarProps {
  isConnected: boolean;
  isDarkMode?: boolean;
  onToggleDarkMode?: () => void;
  onLoadSession?: (sessionId: string) => void;
  onDeleteSession?: (sessionId: string) => void;
  onNewChat?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isConnected,
  isDarkMode,
  onToggleDarkMode,
  onLoadSession,
  onDeleteSession,
  onNewChat
}) => {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [hoveredSession, setHoveredSession] = useState<string | null>(null);


  useEffect(() => {
    const savedSessions = localStorage.getItem('flightlogger-chat-sessions');
    if (savedSessions) {
      try {
        const sessions = JSON.parse(savedSessions).map((session: any) => ({
          ...session,
          timestamp: new Date(session.timestamp),
        }));
        setChatSessions(sessions);
      } catch (error) {
        console.error('Failed to load chat sessions:', error);
      }
    }
  }, []);

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) return diffMins <= 1 ? 'Now' : `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays}d ago`;
    return `${Math.floor(diffDays / 7)}w ago`;
  };

  const handleDeleteSession = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onDeleteSession?.(sessionId);
    setChatSessions(prev => prev.filter(s => s.id !== sessionId));
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="logo-container">
          <img
            src="/fl-logo.svg"
            alt="FlightLogger"
            className="logo"
          />
          <h1 className="app-title">
            FlightLogger Assistant
          </h1>
        </div>
        <p className="app-description">
          Interact with FlightLogger through simple conversation
        </p>
        <div className="status-controls">
          <div className="status-indicator">
            <div className={`status-dot ${isConnected ? 'connected' : 'disconnected'}`}></div>
            <span>
              {isConnected ? 'ONLINE' : 'CONNECTING...'}
            </span>
          </div>
          {onToggleDarkMode && (
            <button
              onClick={onToggleDarkMode}
              className="dark-mode-toggle"
              title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
            >
              {isDarkMode ? '☀️' : '🌙'}
            </button>
          )}
        </div>
      </div>

      <div className="conversations-section">
        <div className="conversations-header">
          <h3 className="conversations-title">
            Previous Chats
          </h3>
          {onNewChat && (
            <button
              onClick={onNewChat}
              className="new-chat-btn"
            >
              New Chat
            </button>
          )}
        </div>
        <div>
          <div
            className="conversation-item"
            onMouseEnter={() => setHoveredSession('current')}
            onMouseLeave={() => setHoveredSession(null)}
          >
            <div className="conversation-content">
              <div className="conversation-title">
                Current Session
              </div>
              <div className="conversation-time">
                Now
              </div>
            </div>
          </div>

          {chatSessions.map((session) => (
            <div
              key={session.id}
              className="conversation-item"
              onClick={() => onLoadSession?.(session.id)}
              onMouseEnter={() => setHoveredSession(session.id)}
              onMouseLeave={() => setHoveredSession(null)}
            >
              <div className="conversation-content">
                <div className="conversation-title">
                  {session.title}
                </div>
                <div className="conversation-time">
                  {formatRelativeTime(session.timestamp)}
                </div>
              </div>
              {hoveredSession === session.id && (
                <button
                  onClick={(e) => handleDeleteSession(session.id, e)}
                  className="delete-btn"
                  title="Delete conversation"
                >
                  ×
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
