import React from 'react';
import { ChatHistory } from '../types';

interface SidebarProps {
  isConnected: boolean;
}

const mockChatHistory: ChatHistory[] = [
  { id: '1', title: 'Current Session', time: 'Now', active: true },
  { id: '2', title: 'Aircraft Maintenance Check', time: '2h ago' },
  { id: '3', title: 'Fleet Status Review', time: 'Yesterday' },
  { id: '4', title: 'Booking Conflicts', time: '3d ago' },
  { id: '5', title: 'Aircraft Search', time: '1w ago' },
];

export const Sidebar: React.FC<SidebarProps> = ({ isConnected }) => {
  return (
    <div className="sidebar">
      <div className="brand-section">
        <div className="logo-container">
          <img src="/fl_vector.png" alt="FlightLogger" className="logo-icon" />
          <div className="brand-text">
            <h1>FlightLogger</h1>
            <p className="tagline">Chat Assistant</p>
          </div>
        </div>
        <p className="subtitle">Interact with FlightLogger through simple conversation</p>
        <div className="status">
          <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
            <span className="status-dot"></span>
            {isConnected ? 'ONLINE' : 'CONNECTING...'}
          </span>
        </div>
      </div>

      <div className="chat-history">
        <h3>Previous Chats</h3>
        <div className="chat-list">
          {mockChatHistory.map((chat) => (
            <div key={chat.id} className={`chat-item ${chat.active ? 'active' : ''}`}>
              <span className="chat-title">{chat.title}</span>
              <span className="chat-time">{chat.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
