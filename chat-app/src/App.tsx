import React, { useEffect, useState } from 'react';
import './App.css';
import { ChatContainer } from './components/ChatContainer';
import { Sidebar } from './components/Sidebar';
import { useChat } from './hooks/useChat';

const App: React.FC = () => {
  const {
    messages,
    isLoading,
    isConnected,
    messagesEndRef,
    initializeChat,
    sendMessage,
    loadSession,
    deleteSession,
    newChat
  } = useChat();
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    initializeChat();
  }, []);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <div className={`app-container ${isDarkMode ? 'dark' : ''}`}>
      <Sidebar
        isConnected={isConnected}
        isDarkMode={isDarkMode}
        onToggleDarkMode={toggleDarkMode}
        onLoadSession={loadSession}
        onDeleteSession={deleteSession}
        onNewChat={newChat}
      />
      <ChatContainer
        messages={messages}
        isLoading={isLoading}
        isConnected={isConnected}
        messagesEndRef={messagesEndRef}
        onSendMessage={sendMessage}
      />
    </div>
  );
};

export default App;
