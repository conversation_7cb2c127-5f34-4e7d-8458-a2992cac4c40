import React, { useEffect, useState } from 'react';
import './App.css';
import { ChatContainer } from './components/ChatContainer';
import { Sidebar } from './components/Sidebar';
import { useChat } from './hooks/useChat';

const App: React.FC = () => {
  const { messages, isLoading, isConnected, messagesEndRef, initializeChat, sendMessage } = useChat();
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    initializeChat();
  }, []);

  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <div className={`app ${isDarkMode ? 'dark' : ''}`}>
      <Sidebar
        isConnected={isConnected}
        isDarkMode={isDarkMode}
        onToggleDarkMode={toggleDarkMode}
      />
      <ChatContainer
        messages={messages}
        isLoading={isLoading}
        isConnected={isConnected}
        messagesEndRef={messagesEndRef}
        onSendMessage={sendMessage}
      />
    </div>
  );
};

export default App;
