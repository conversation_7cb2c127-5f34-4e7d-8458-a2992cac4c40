import React, { useEffect } from 'react';
import { ChatContainer } from './components/ChatContainer';
import { Sidebar } from './components/Sidebar';
import { useChat } from './hooks/useChat';

const App: React.FC = () => {
  const { messages, isLoading, isConnected, messagesEndRef, initializeChat, sendMessage } = useChat();

  useEffect(() => {
    initializeChat();
  }, []);

  return (
    <div className="app">
      <Sidebar isConnected={isConnected} />
      <ChatContainer
        messages={messages}
        isLoading={isLoading}
        isConnected={isConnected}
        messagesEndRef={messagesEndRef}
        onSendMessage={sendMessage}
      />
    </div>
  );
};

export default App;
