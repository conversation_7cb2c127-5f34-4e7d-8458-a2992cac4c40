import { useEffect, useRef, useState } from 'react';
import { MCPService, OllamaService } from '../services/api';
import { Message } from '../types';

let messageIdCounter = 0;

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const initializationRef = useRef(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (type: Message['type'], content: string, isSystem = false) => {
    setMessages((prev) => [
      ...prev,
      {
        id: Date.now() + (++messageIdCounter),
        type,
        content,
        isSystem,
        timestamp: new Date().toISOString(),
      },
    ]);
  };

  const initializeChat = async () => {
    if (initializationRef.current) return;
    initializationRef.current = true;

    try {
      addMessage('system', '🚀 Connecting to FlightLogger Assistant...', true);

      addMessage(
        'ai',
        '# Hello from FlightLogger Assistant!\n\nI can help you with:\n\n- **Aircraft Management**: View and search your fleet\n- **Maintenance Booking**: Schedule maintenance for aircraft\n- **Conflict Detection**: Check for scheduling conflicts\n\n*Ready to assist you!*',
      );

      // Simple connection - just set to connected
      setIsConnected(true);
      addMessage('system', '✅ Connected! You can now ask about your aircraft fleet.', true);
    } catch (error) {
      addMessage('system', `❌ Error: ${(error as Error).message}`, true);
    }
  };

  const analyzeUserIntent = async (message: string, availableTools: any[]) => {
    const intentPrompt = `Analyze this user request and determine which FlightLogger tools to use.

AVAILABLE TOOLS:
${availableTools.map((tool) => `- ${tool.name}: ${tool.description}`).join('\n')}

USER REQUEST: "${message}"

Respond with ONLY a JSON object in this format:
{
  "tools": [
    {
      "name": "tool_name",
      "arguments": { "key": "value" }
    }
  ],
  "reasoning": "brief explanation"
}

Examples:
- "Show me all aircraft" → {"tools": [{"name": "list_aircraft", "arguments": {}}], "reasoning": "User wants to see all aircraft"}
- "Find aircraft OY-ABC" → {"tools": [{"name": "find_aircraft", "arguments": {"search_term": "OY-ABC"}}], "reasoning": "User wants to find specific aircraft"}
- "Book maintenance for OY-ABC from 2024-01-15 to 2024-01-16" → {"tools": [{"name": "create_maintenance_booking", "arguments": {"aircraft_call_sign": "OY-ABC", "start_time": "2024-01-15T09:00:00Z", "end_time": "2024-01-16T17:00:00Z"}}], "reasoning": "User wants to book maintenance"}`;

    try {
      const response = await OllamaService.generate(intentPrompt);
      return JSON.parse(response.trim());
    } catch (error) {
      console.error('Intent analysis failed:', error);
      return { tools: [], reasoning: 'Could not analyze intent' };
    }
  };

  const executeTools = async (toolCalls: any[]) => {
    const results = [];

    for (const toolCall of toolCalls) {
      try {
        const result = await MCPService.callTool(toolCall.name, toolCall.arguments || {});
        results.push({
          tool: toolCall.name,
          success: result.success,
          data: result,
          arguments: toolCall.arguments
        });
      } catch (error) {
        results.push({
          tool: toolCall.name,
          success: false,
          error: (error as Error).message,
          arguments: toolCall.arguments
        });
      }
    }

    return results;
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    addMessage('user', message);
    setIsLoading(true);

    try {
      // Get available MCP tools
      const tools = await MCPService.listTools();

      // Create a prompt that includes tool information
      const toolsDescription = tools.length > 0
        ? `\n\nAvailable tools:\n${MCPService.formatToolsForLLM(tools)}\n\nIMPORTANT: If the user is asking about aircraft, maintenance, or conflicts, you MUST use one of the tools above.

Respond with ONLY valid JSON in this exact format:
{"tool": "ToolName", "arguments": {}}

Examples:
- For listing aircraft: {"tool": "ListAircraftTool", "arguments": {}}
- For finding aircraft: {"tool": "FindAircraftTool", "arguments": {"search_term": "AC-A"}}
- For creating maintenance booking: {"tool": "CreateMaintenanceBookingTool", "arguments": {"aircraft_call_sign": "AC-A", "start_time": "2025-01-15T09:00:00Z", "end_time": "2025-01-15T17:00:00Z", "description": "engine inspection"}}
- For checking conflicts: {"tool": "CheckMaintenanceConflictsTool", "arguments": {"aircraft_call_sign": "AC-A", "start_time": "2025-01-15T09:00:00Z", "end_time": "2025-01-15T17:00:00Z"}}

Make sure the JSON is complete and valid. Include ALL required arguments as shown in the tool descriptions above.

If the user is asking general questions, respond normally with text (not JSON).`
        : '';

      const fullPrompt = `You are FlightLogger Assistant, an AI assistant for aircraft management. ${toolsDescription}\n\nUser: ${message}\n\nAssistant:`;

      // Get response from Ollama
      const aiResponse = await OllamaService.generate(fullPrompt);

      // Check if the response is a tool call
      const trimmedResponse = aiResponse.trim();
      if (trimmedResponse.startsWith('{') && trimmedResponse.includes('"tool"')) {
        try {
          const toolCall = JSON.parse(trimmedResponse);
          if (toolCall.tool && typeof toolCall.arguments === 'object') {
            addMessage('system', `🔧 Using tool: ${toolCall.tool}`, true);

            // Execute the tool
            const toolResult = await MCPService.callTool(toolCall.tool, toolCall.arguments || {});

            console.log('Tool result:', toolResult);

            if (toolResult.success) {
              // Get a natural language response about the tool result
              const resultPrompt = `The user asked: "${message}"\n\nI used the ${toolCall.tool} tool and got this result: ${JSON.stringify(toolResult)}\n\nPlease provide a helpful, natural language response to the user based on this result:`;
              console.log('Generating final response with prompt:', resultPrompt.substring(0, 200) + '...');
              const finalResponse = await OllamaService.generate(resultPrompt);
              console.log('Final response:', finalResponse);
              addMessage('ai', finalResponse);
            } else {
              console.log('Tool failed:', toolResult.message);
              addMessage('ai', `I tried to help but encountered an error: ${toolResult.message}`);
            }
          } else {
            addMessage('ai', trimmedResponse);
          }
        } catch (parseError) {
          console.log('Failed to parse as JSON, treating as normal response:', parseError);
          addMessage('ai', trimmedResponse);
        }
      } else {
        addMessage('ai', trimmedResponse);
      }
    } catch (error) {
      addMessage('system', `❌ Error: ${(error as any).message}`, true);
    }

    setIsLoading(false);
  };

  const saveCurrentSession = () => {
    if (messages.length === 0) return;

    const sessionTitle = messages.find(m => m.type === 'user')?.content.slice(0, 50) + '...' || 'New Conversation';
    const session: ChatSession = {
      id: `session-${Date.now()}`,
      title: sessionTitle,
      timestamp: new Date(),
      messages: [...messages]
    };

    const existingSessions = localStorage.getItem('flightlogger-chat-sessions');
    const sessions = existingSessions ? JSON.parse(existingSessions) : [];
    sessions.unshift(session);

    // Keep only the last 20 sessions
    if (sessions.length > 20) {
      sessions.splice(20);
    }

    localStorage.setItem('flightlogger-chat-sessions', JSON.stringify(sessions));
  };

  const loadSession = (sessionId: string) => {
    const existingSessions = localStorage.getItem('flightlogger-chat-sessions');
    if (existingSessions) {
      const sessions = JSON.parse(existingSessions);
      const session = sessions.find((s: ChatSession) => s.id === sessionId);
      if (session) {
        setMessages(session.messages);
      }
    }
  };

  const deleteSession = (sessionId: string) => {
    const existingSessions = localStorage.getItem('flightlogger-chat-sessions');
    if (existingSessions) {
      const sessions = JSON.parse(existingSessions);
      const filteredSessions = sessions.filter((s: ChatSession) => s.id !== sessionId);
      localStorage.setItem('flightlogger-chat-sessions', JSON.stringify(filteredSessions));
    }
  };

  const newChat = () => {
    if (messages.length > 0) {
      saveCurrentSession();
    }
    setMessages([]);
    initializeChat();
  };

  return {
    messages,
    isLoading,
    isConnected,
    messagesEndRef,
    addMessage,
    initializeChat,
    sendMessage,
    saveCurrentSession,
    loadSession,
    deleteSession,
    newChat,
  };
};
