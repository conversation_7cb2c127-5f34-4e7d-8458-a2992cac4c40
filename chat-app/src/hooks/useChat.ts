import { useEffect, useRef, useState } from 'react';
import { MCPService, OllamaService } from '../services/api';
import { Message } from '../types';

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const initializationRef = useRef(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (type: Message['type'], content: string, isSystem = false) => {
    setMessages((prev) => [
      ...prev,
      {
        id: Date.now(),
        type,
        content,
        isSystem,
        timestamp: new Date().toLocaleTimeString(),
      },
    ]);
  };

  const initializeChat = async () => {
    if (initializationRef.current) return;
    initializationRef.current = true;

    try {
      addMessage('system', '🚀 Connecting to FlightLogger MCP Server and Ollama...', true);

      addMessage(
        'ai',
        '# Hello from FlightLogger Assistant!\n\nI can help you with:\n\n- **Aircraft Management**: View and search your fleet\n- **Maintenance Booking**: Schedule maintenance for aircraft\n- **Conflict Detection**: Check for scheduling conflicts\n\n*Ready to assist you!*',
      );

      await MCPService.listTools();

      const aircraftData = await MCPService.callTool('list_aircraft');

      if (aircraftData.success && aircraftData.aircraft) {
        setIsConnected(true);
        addMessage('system', '✅ Connected! You can now ask about your aircraft fleet.', true);
      } else {
        throw new Error('Failed to get aircraft data');
      }
    } catch (error) {
      addMessage('system', `❌ Error: ${(error as Error).message}`, true);
    }
  };

  const analyzeUserIntent = async (message: string, availableTools: any[]) => {
    const intentPrompt = `Analyze this user request and determine which FlightLogger tools to use.

AVAILABLE TOOLS:
${availableTools.map((tool) => `- ${tool.name}: ${tool.description}`).join('\n')}

USER REQUEST: "${message}"

Respond with ONLY a JSON object in this format:
{
  "tools": [
    {
      "name": "tool_name",
      "arguments": { "key": "value" }
    }
  ],
  "reasoning": "brief explanation"
}

Examples:
- "Show me all aircraft" → {"tools": [{"name": "list_aircraft", "arguments": {}}], "reasoning": "User wants to see all aircraft"}
- "Find aircraft OY-ABC" → {"tools": [{"name": "find_aircraft", "arguments": {"search_term": "OY-ABC"}}], "reasoning": "User wants to find specific aircraft"}
- "Book maintenance for OY-ABC from 2024-01-15 to 2024-01-16" → {"tools": [{"name": "create_maintenance_booking", "arguments": {"aircraft_call_sign": "OY-ABC", "start_time": "2024-01-15T09:00:00Z", "end_time": "2024-01-16T17:00:00Z"}}], "reasoning": "User wants to book maintenance"}`;

    try {
      const response = await OllamaService.generate(intentPrompt);
      return JSON.parse(response.trim());
    } catch (error) {
      console.error('Intent analysis failed:', error);
      return { tools: [], reasoning: 'Could not analyze intent' };
    }
  };

  const executeTools = async (toolCalls: any[]) => {
    const results = [];

    for (const toolCall of toolCalls) {
      try {
        const result = await MCPService.callTool(toolCall.name, toolCall.arguments || {});
        results.push({
          tool: toolCall.name,
          success: result.success,
          data: result,
          arguments: toolCall.arguments
        });
      } catch (error) {
        results.push({
          tool: toolCall.name,
          success: false,
          error: (error as Error).message,
          arguments: toolCall.arguments
        });
      }
    }

    return results;
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    addMessage('user', message);
    setIsLoading(true);

    try {
      const availableTools = await MCPService.listTools();

      // Analyze user intent to determine which tools to use
      const intent = await analyzeUserIntent(message, availableTools);

      let toolResults = '';

      if (intent.tools && intent.tools.length > 0) {
        addMessage('system', `🔧 Using tools: ${intent.tools.map((t: any) => t.name).join(', ')}`, true);

        const results = await executeTools(intent.tools);

        toolResults = results.map(result => {
          if (result.success) {
            return `[TOOL: ${result.tool}] SUCCESS: ${JSON.stringify(result.data, null, 2)}`;
          } else {
            return `[TOOL: ${result.tool}] ERROR: ${result.error}`;
          }
        }).join('\n\n');
      } else {
        toolResults = '[NO TOOLS USED] Responding with general knowledge';
      }

      const systemPrompt = `You are the FlightLogger Assistant. Based on the tool execution results below, provide a helpful, natural response to the user.

AVAILABLE TOOLS:
${availableTools.map((tool) => `- ${tool.name}: ${tool.description}`).join('\n')}

USER REQUEST: ${message}

TOOL EXECUTION RESULTS:
${toolResults}

INSTRUCTIONS:
- Provide a natural, conversational response based on the tool results
- If tools were successful, summarize the data in a user-friendly way
- If tools failed, explain what went wrong and suggest alternatives
- Always be helpful and professional`;

      const aiResponse = await OllamaService.generate(systemPrompt);
      addMessage('ai', aiResponse);
    } catch (error) {
      addMessage('system', `❌ Error: ${(error as any).message}`, true);
    }

    setIsLoading(false);
  };

  return {
    messages,
    isLoading,
    isConnected,
    messagesEndRef,
    addMessage,
    initializeChat,
    sendMessage,
  };
};
