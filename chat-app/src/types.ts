export interface Message {
  id: number;
  type: 'user' | 'ai' | 'system';
  content: string;
  isSystem: boolean;
  timestamp: string;
}

export interface Aircraft {
  id: string;
  callSign: string;
  model: string;
  registration?: string;
}

export interface MCPResponse<T = any> {
  jsonrpc: string;
  id: number;
  result?: T;
  error?: {
    code: number;
    message: string;
  };
}

export interface MCPToolResponse {
  success: boolean;
  aircraft?: Aircraft[];
  message?: string;
  data?: any;
}

export interface OllamaResponse {
  response: string;
  context: number[];
  done: boolean;
}

export interface ConnectionStatus {
  isConnected: boolean;
  isLoading: boolean;
}

export interface ChatHistory {
  id: string;
  title: string;
  time: string;
  active?: boolean;
}

export interface ChatSession {
  id: string;
  title: string;
  timestamp: Date;
  messages: Message[];
}
