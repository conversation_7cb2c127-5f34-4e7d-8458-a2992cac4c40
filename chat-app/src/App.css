* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
}

.app {
  height: 100vh;
  display: grid;
  grid-template-columns: 3fr 9fr;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background:
    radial-gradient(ellipse at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 40%),
    radial-gradient(ellipse at 70% 60%, rgba(255, 255, 255, 0.12) 0%, transparent 35%),
    radial-gradient(ellipse at 20% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 30%),
    radial-gradient(ellipse at 80% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 25%),
    radial-gradient(ellipse at 50% 90%, rgba(255, 255, 255, 0.06) 0%, transparent 20%),
    radial-gradient(ellipse at 40% 10%, rgba(135, 206, 235, 0.3) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 70%, rgba(100, 149, 237, 0.25) 0%, transparent 45%),
    linear-gradient(135deg, #1e3a8a 0%, #1e40af 25%, #2563eb 50%, #3b82f6 75%, #60a5fa 100%);
  background-size:
    800px 400px,
    600px 300px,
    400px 200px,
    500px 250px,
    300px 150px,
    1000px 500px,
    800px 400px,
    100% 100%;
  background-attachment: fixed;
  animation: cloudDrift 30s ease-in-out infinite;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1.5rem;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(25px);
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset -1px 0 0 rgba(255, 255, 255, 0.2);
}

.brand-section {
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.3));
}

.brand-section h1 {
  color: #ffffff;
  font-size: 1.8rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 400;
  letter-spacing: 0.02em;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  line-height: 1.4;
}

.chat-history {
  flex: 1;
}

.chat-history h3 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.02em;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.chat-item {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-item:hover {
  transform: translateX(0.25rem);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.chat-item.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.chat-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.chat-time {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  font-weight: 400;
  opacity: 0.7;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  border: 1px solid;
}

.status-indicator.connected {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.3);
}

.status-indicator.disconnected {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.status-dot {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

.chat-container {
  display: flex;
  flex-direction: column;
  margin: 2rem;
  border-radius: 1rem;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.messages {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: calc(100vh - 200px);
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 64rem;
}

.user-message {
  align-self: flex-end;
}

.ai-message {
  align-self: flex-start;
}

.user-message .message-content {
  background: #3b82f6;
  color: white;
  padding: 1.5rem;
  border-radius: 1.5rem;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 20px 20px 5px 20px;
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.ai-message .message-content {
  background: rgba(255, 255, 255, 0.8);
  color: #1e293b;
  padding: 1.5rem;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 1.5rem;
  border-radius: 20px 20px 20px 5px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.system {
  align-self: center;
  margin: 0 auto;
  max-width: 64rem;
}

.system .message-content {
  background: rgba(59, 130, 246, 0.05);
  color: rgba(59, 130, 246, 0.7);
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.85rem;
  text-align: center;
  max-width: 100%;
  border: 1px solid rgba(59, 130, 246, 0.15);
  font-weight: 400;
  letter-spacing: 0.02em;
  opacity: 0.8;
}

.system .message-time {
  display: none;
}

.message-time {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.5rem;
}

.user-message .message-time {
  align-self: flex-end;
}

.ai-message .message-time {
  align-self: flex-start;
}

.input-container {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
}

.message-input {
  flex: 1;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.75rem;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background: rgba(255, 255, 255, 0.2);
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.message-input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.2),
    0 0 20px rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.25);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.1);
}

.send-button {
  padding: 1.5rem;
  background: #3b82f6;
  color: white;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
  cursor: pointer;
}

.send-button:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(59, 130, 246, 0.3);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #6b7280;
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes cloudDrift {
  0%,
  100% {
    background-position:
      0% 0%,
      100% 100%,
      50% 50%,
      0% 0%,
      40% 70%,
      10% 90%,
      30% 60%,
      60% 40%,
      0% 0%;
  }
  50% {
    background-position:
      20% 10%,
      80% 90%,
      30% 70%,
      10% 90%,
      30% 60%,
      60% 40%,
      0% 0%;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
