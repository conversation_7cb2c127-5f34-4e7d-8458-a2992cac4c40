
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: transparent;
  height: 100vh;
  overflow: hidden;
}

.app-container {
  height: 100vh;
  display: grid;
  grid-template-columns: 1fr 3fr;
  background:
    radial-gradient(ellipse 1200px 800px at 15% 25%, rgba(255, 255, 255, 0.12) 0%, transparent 45%),
    radial-gradient(ellipse 900px 600px at 85% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse 700px 500px at 65% 15%, rgba(255, 255, 255, 0.08) 0%, transparent 55%),
    radial-gradient(ellipse 500px 350px at 35% 85%, rgba(255, 255, 255, 0.06) 0%, transparent 60%),
    radial-gradient(ellipse 600px 400px at 50% 50%, rgba(255, 255, 255, 0.04) 0%, transparent 65%),
    radial-gradient(ellipse 400px 300px at 80% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(ellipse 350px 250px at 20% 70%, rgba(255, 255, 255, 0.03) 0%, transparent 55%),
    linear-gradient(-45deg, #dbeafe, #bfdbfe, #93c5fd, #60a5fa, #3b82f6);
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 400% 400%;
  animation: gradientShift 35s ease-in-out infinite, cloudDrift 50s linear infinite;
  position: relative;
}

.app-container.dark {
  background:
    radial-gradient(ellipse 1200px 800px at 20% 30%, rgba(0, 0, 0, 0.35) 0%, transparent 50%),
    radial-gradient(ellipse 900px 600px at 80% 70%, rgba(0, 0, 0, 0.3) 0%, transparent 55%),
    radial-gradient(ellipse 700px 500px at 60% 20%, rgba(0, 0, 0, 0.25) 0%, transparent 60%),
    radial-gradient(ellipse 500px 350px at 40% 80%, rgba(0, 0, 0, 0.2) 0%, transparent 65%),
    radial-gradient(ellipse 600px 400px at 70% 50%, rgba(0, 0, 0, 0.18) 0%, transparent 70%),
    radial-gradient(ellipse 400px 300px at 30% 60%, rgba(0, 0, 0, 0.15) 0%, transparent 60%),
    radial-gradient(ellipse 350px 250px at 90% 40%, rgba(0, 0, 0, 0.12) 0%, transparent 65%),
    linear-gradient(-45deg, #0f172a, #1e293b, #334155, #475569, #1e3a8a);
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 400% 400%;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%;
  }
  16% {
    background-position: 80% 30%, 15% 70%, 90% 20%, 35% 85%, 50% 50%, 80% 30%, 20% 70%, 80% 30%;
  }
  32% {
    background-position: 100% 80%, 70% 40%, 30% 90%, 100% 80%, 60% 80%, 40% 20%, 5% 15%, 100% 80%;
  }
  48% {
    background-position: 20% 100%, 90% 10%, 60% 70%, 20% 100%, 70% 50%, 30% 60%, 90% 40%, 20% 100%;
  }
  64% {
    background-position: 60% 20%, 40% 80%, 10% 40%, 60% 20%, 40% 80%, 10% 40%, 60% 20%, 60% 20%;
  }
  80% {
    background-position: 90% 60%, 10% 30%, 80% 10%, 90% 60%, 10% 30%, 80% 10%, 90% 60%, 90% 60%;
  }
  100% {
    background-position: 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%, 0% 50%;
  }
}

@keyframes cloudDrift {
  0% {
    background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 50%;
  }
  14% {
    background-position: 20% 10%, -10% 5%, 8% -5%, 35% 85%, 50% 50%, 80% 30%, 20% 70%, 70% 40%;
  }
  28% {
    background-position: 40% 20%, 25% -8%, -5% 12%, 70% 40%, 30% 90%, 40% 20%, -5% 15%, 100% 70%;
  }
  42% {
    background-position: 60% 30%, 50% 15%, 20% 25%, 100% 80%, 60% 80%, 20% 60%, 25% 35%, 30% 90%;
  }
  56% {
    background-position: 80% 40%, 75% 35%, 40% 50%, 30% 90%, 70% 50%, 60% 25%, 50% 80%, 80% 20%;
  }
  70% {
    background-position: 100% 50%, 90% 45%, 60% 70%, 60% 20%, 40% 80%, 90% 40%, 75% 45%, 20% 100%;
  }
  84% {
    background-position: 80% 60%, 70% 55%, 80% 90%, 90% 60%, 10% 30%, 70% 55%, 90% 60%, 60% 20%;
  }
  100% {
    background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 50%;
  }
}

.floating-dark-mode-toggle {
  position: fixed;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.25rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.floating-dark-mode-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.dark .floating-dark-mode-toggle {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.sidebar {
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.logo {
  height: 2rem;
  width: auto;
  filter: hue-rotate(25deg) saturate(1.2) brightness(1.1);
  transition: filter 0.3s ease;
}

.logo:hover {
  filter: hue-rotate(35deg) saturate(1.4) brightness(1.2);
}

.app-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.app-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0.5rem 0 1rem 0;
}

.status-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #059669;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.status-dot.connected {
  background-color: #10b981;
  animation: pulse 2s infinite;
}

.status-dot.disconnected {
  background-color: #ef4444;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.dark-mode-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.dark-mode-toggle:hover {
  background: #f3f4f6;
}

.conversations-section {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  max-height: 35vh;
  min-height: 200px;
}

.conversations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.conversations-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.new-chat-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-chat-btn:hover {
  background: #2563eb;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-header h3 {
  font-size: 0.875rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.new-item-btn {
  padding: 0.375rem 0.75rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 0.75rem; /* Match other buttons */
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.new-item-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Dark mode new buttons */
.dark .new-item-btn {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  box-shadow: 0 2px 4px rgba(234, 88, 12, 0.2);
}

.dark .new-item-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: 0 4px 12px rgba(234, 88, 12, 0.4);
}

.conversation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 0.75rem; /* Match workflow items */
  cursor: pointer;
  transition: all 0.2s ease; /* Match workflow items */
  border: 1px solid #e5e7eb; /* Add visible border like workflow items */
  background: white; /* Explicit background */
}

.conversation-item:hover {
  background: #f9fafb;
  border-color: #60a5fa; /* Blue for chats */
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.1); /* Blue shadow for chats */
}

.conversation-content {
  flex: 1;
}

.conversation-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.conversation-time {
  color: #6b7280;
  font-size: 0.75rem;
}

.delete-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.conversation-item:hover .delete-btn {
  opacity: 1;
}

.delete-btn:hover {
  background: #dc2626;
}


.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 2rem;
  background: transparent;
}

.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 60rem;
  margin: 0 auto;
  width: 100%;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.messages-container {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}


.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.message.user {
  align-self: flex-end;
}

.message.assistant {
  align-self: flex-start;
}

.message.system {
  align-self: center;
  max-width: 100%;
}

.message-content {
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  word-wrap: break-word;
}

.message.user .message-content {
  background: #3b82f6;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.message.assistant .message-content {
  background: #f3f4f6;
  color: #1f2937;
  border-bottom-left-radius: 0.25rem;
}

.message.system .message-content {
  background: #fef3c7;
  color: #92400e;
  text-align: center;
  font-size: 0.875rem;
  border-radius: 0.5rem;
}

.message-time {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
  text-align: right;
}

.message.assistant .message-time {
  text-align: left;
}

.message.system .message-time {
  text-align: center;
}


.chat-input-container {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: white;
}

.chat-input-form {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  background: white;
  color: #1f2937;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  height: 3rem; /* Fixed height for consistency */
}

.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.chat-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.chat-input::placeholder {
  color: #9ca3af;
}

.send-btn {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 0;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 3rem; /* Match input height */
  min-width: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #9ca3af;
}

/* Dark mode send button */
.dark .send-btn {
  background: linear-gradient(135deg, #ea580c, #dc2626);
}

.dark .send-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: 0 4px 12px rgba(234, 88, 12, 0.3);
}


.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}


.dark .sidebar {
  background: #1f2937;
  border-right-color: #374151;
}

.dark .sidebar-header {
  border-bottom-color: #374151;
}

.dark .app-title {
  color: #f9fafb;
}

.dark .app-description {
  color: #9ca3af;
}

.dark .conversations-title {
  color: #f9fafb;
}

.dark .conversation-item {
  color: #f9fafb;
  background: #374151;
  border-color: #4b5563;
}

.dark .conversation-item:hover {
  background: #4b5563;
  border-color: #60a5fa; /* Blue for chats in dark mode */
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
}

.dark .conversation-title {
  color: #f9fafb;
}

.dark .conversation-time {
  color: #9ca3af;
}

.dark .dark-mode-toggle:hover {
  background: #374151;
}

.dark .chat-window {
  background: #1f2937;
}

.dark .message.assistant .message-content {
  background: #374151;
  color: #f9fafb;
}

.dark .chat-input-container {
  background: #1f2937;
  border-top-color: #374151;
}

.dark .chat-input {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .chat-input:focus {
  border-color: #3b82f6;
}

.dark .chat-input::placeholder {
  color: #6b7280;
}

.workflows-section {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.workflows-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.workflows-header h3 {
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  letter-spacing: 0.05em;
}

.workflows-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.workflow-item {
  padding: 0.75rem;
  background: white;
  border-radius: 0.75rem; /* Match conversation items */
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  cursor: pointer;
}

.workflow-item:hover {
  border-color: #ea580c; /* Orange for workflows */
  box-shadow: 0 2px 8px rgba(234, 88, 12, 0.1); /* Orange shadow for workflows */
}

.workflow-content h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.workflow-content p {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.3;
}

.create-workflow-btn {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-workflow-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.dark .workflows-section {
  background: #1f2937;
  border-top-color: #374151;
}

.dark .section-header {
  border-bottom-color: #374151;
}

.dark .section-header h3 {
  color: #f9fafb;
}

.dark .workflow-item {
  background: #374151;
  border-color: #4b5563;
}

.dark .workflow-item:hover {
  border-color: #ea580c; /* Orange for workflows in dark mode */
  box-shadow: 0 2px 8px rgba(234, 88, 12, 0.2);
}

.dark .workflow-content h4 {
  color: #f9fafb;
}

.dark .workflow-content p {
  color: #9ca3af;
}

.tools-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  gap: 1rem;
}

.chat-title-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: -0.025em;
}



.tools-menu-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem; /* Match other buttons */
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tools-menu-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.tools-menu-icon {
  font-size: 1rem;
}

.dark .tools-bar {
  background: #1f2937;
  border-bottom-color: #374151;
}

.dark .chat-title {
  color: #f9fafb;
}

.dark .tools-menu-btn {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  border-color: #ea580c;
  color: #f9fafb;
}

.dark .tools-menu-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(234, 88, 12, 0.3);
}
