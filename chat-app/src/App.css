
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  height: 100vh;
  overflow: hidden;
}

.app-container {
  height: 100vh;
  display: grid;
  grid-template-columns: 1fr 3fr;
  background: linear-gradient(-45deg, #1e3a8a, #1e40af, #2563eb, #3b82f6, #1d4ed8);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.sidebar {
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.logo {
  height: 2rem;
  width: auto;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.app-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0.5rem 0 1rem 0;
}

.status-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #059669;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.status-dot.connected {
  background-color: #10b981;
  animation: pulse 2s infinite;
}

.status-dot.disconnected {
  background-color: #ef4444;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.dark-mode-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.dark-mode-toggle:hover {
  background: #f3f4f6;
}

.conversations-section {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.conversations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.conversations-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.new-chat-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-chat-btn:hover {
  background: #2563eb;
}

.conversation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid transparent;
}

.conversation-item:hover {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.conversation-content {
  flex: 1;
}

.conversation-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.conversation-time {
  color: #6b7280;
  font-size: 0.75rem;
}

.delete-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.conversation-item:hover .delete-btn {
  opacity: 1;
}

.delete-btn:hover {
  background: #dc2626;
}


.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 2rem;
  background: transparent;
}

.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 60rem;
  margin: 0 auto;
  width: 100%;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.messages-container {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}


.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.message.user {
  align-self: flex-end;
}

.message.assistant {
  align-self: flex-start;
}

.message.system {
  align-self: center;
  max-width: 100%;
}

.message-content {
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  word-wrap: break-word;
}

.message.user .message-content {
  background: #3b82f6;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.message.assistant .message-content {
  background: #f3f4f6;
  color: #1f2937;
  border-bottom-left-radius: 0.25rem;
}

.message.system .message-content {
  background: #fef3c7;
  color: #92400e;
  text-align: center;
  font-size: 0.875rem;
  border-radius: 0.5rem;
}

.message-time {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
  text-align: right;
}

.message.assistant .message-time {
  text-align: left;
}

.message.system .message-time {
  text-align: center;
}


.chat-input-container {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: white;
}

.chat-input-form {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  background: white;
  color: #1f2937;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.chat-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.chat-input::placeholder {
  color: #9ca3af;
}

.send-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #2563eb;
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}


.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}


.dark .sidebar {
  background: #1f2937;
  border-right-color: #374151;
}

.dark .sidebar-header {
  border-bottom-color: #374151;
}

.dark .app-title {
  color: #f9fafb;
}

.dark .app-description {
  color: #9ca3af;
}

.dark .conversations-title {
  color: #f9fafb;
}

.dark .conversation-item {
  color: #f9fafb;
}

.dark .conversation-item:hover {
  background: #374151;
  border-color: #4b5563;
}

.dark .conversation-title {
  color: #f9fafb;
}

.dark .conversation-time {
  color: #9ca3af;
}

.dark .dark-mode-toggle:hover {
  background: #374151;
}

.dark .chat-window {
  background: #1f2937;
}

.dark .message.assistant .message-content {
  background: #374151;
  color: #f9fafb;
}

.dark .chat-input-container {
  background: #1f2937;
  border-top-color: #374151;
}

.dark .chat-input {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .chat-input:focus {
  border-color: #3b82f6;
}

.dark .chat-input::placeholder {
  color: #6b7280;
}
