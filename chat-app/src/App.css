* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
}

/* App Layout */
.app {
  height: 100vh;
  display: grid;
  grid-template-columns: 3fr 9fr;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #1e40af;
}

.app.dark {
  background: #1f2937;
}

/* Sidebar */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1.5rem;
  overflow-y: auto;
  background: white;
  border-right: 1px solid #e5e7eb;
}

.dark .sidebar {
  background: #1f2937;
  border-right: 1px solid #374151;
  color: white;
}

/* Brand Section */
.brand-section {
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.dark .brand-section {
  border-bottom: 1px solid #374151;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.3));
}

.logo-container h1 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin: 0;
}

.dark .logo-container h1 {
  color: white;
}

.subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.dark .subtitle {
  color: #9ca3af;
}

/* Status */
.status {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-indicator.connected {
  color: #059669;
}

.status-indicator.disconnected {
  color: #dc2626;
}

.status-dot {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

.dark-mode-toggle {
  padding: 0.5rem;
  border-radius: 0.5rem;
  background: #f3f4f6;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 1rem;
}

.dark-mode-toggle:hover {
  background: #e5e7eb;
}

.dark .dark-mode-toggle {
  background: #374151;
}

.dark .dark-mode-toggle:hover {
  background: #4b5563;
}

/* Chat History */
.chat-history h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.dark .chat-history h3 {
  color: #d1d5db;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.chat-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-item:hover {
  background: #f9fafb;
}

.dark .chat-item:hover {
  background: #374151;
}

.chat-item.active {
  background: #eff6ff;
  border: 1px solid #dbeafe;
}

.dark .chat-item.active {
  background: #1e3a8a;
  border: 1px solid #3b82f6;
}

.chat-title {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.dark .chat-title {
  color: #f9fafb;
}

.chat-time {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.dark .chat-time {
  color: #9ca3af;
}

/* Chat Container */
.chat-container {
  display: flex;
  flex-direction: column;
  margin: 2rem;
  border-radius: 1rem;
  overflow: hidden;
  background: white;
  border: 1px solid #e5e7eb;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .chat-container {
  background: #1f2937;
  border: 1px solid #374151;
}

/* Messages */
.messages {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: calc(100vh - 200px);
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 64rem;
}

.user-message {
  align-self: flex-end;
}

.ai-message {
  align-self: flex-start;
}

.user-message .message-content {
  background: #3b82f6;
  color: white;
  padding: 1.5rem;
  border: 1px solid #2563eb;
  border-radius: 20px 20px 5px 20px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.dark .user-message .message-content {
  background: #2563eb;
  border: 1px solid #1d4ed8;
}

.ai-message .message-content {
  background: #f8fafc;
  color: #1e293b;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 20px 20px 20px 5px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.dark .ai-message .message-content {
  background: #374151;
  color: #f1f5f9;
  border: 1px solid #4b5563;
}

/* System Messages */
.system {
  align-self: center;
  margin: 0 auto;
  max-width: 64rem;
}

.system .message-content {
  background: #eff6ff;
  color: #1e40af;
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.85rem;
  text-align: center;
  max-width: 100%;
  border: 1px solid #dbeafe;
  font-weight: 400;
  letter-spacing: 0.02em;
  opacity: 0.9;
}

.dark .system .message-content {
  background: #1e3a8a;
  color: #93c5fd;
  border: 1px solid #1e40af;
}

.system .message-time {
  display: none;
}

.message-time {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.5rem;
}

.user-message .message-time {
  align-self: flex-end;
}

.ai-message .message-time {
  align-self: flex-start;
}

/* Input Container */
.input-container {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.dark .input-container {
  border-top: 1px solid #374151;
  background: #1f2937;
}

.message-input {
  flex: 1;
  padding: 1.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  color: #111827;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: white;
}

.dark .message-input {
  border: 1px solid #4b5563;
  color: #f9fafb;
  background: #374151;
}

.message-input::placeholder {
  color: #6b7280;
}

.dark .message-input::placeholder {
  color: #9ca3af;
}

.message-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.dark .message-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f3f4f6;
}

.dark .message-input:disabled {
  background: #1f2937;
}

.send-button {
  padding: 1.5rem;
  background: #3b82f6;
  color: white;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid #2563eb;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
  cursor: pointer;
}

.send-button:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(59, 130, 246, 0.3);
}

.dark .send-button {
  background: #2563eb;
  border: 1px solid #1d4ed8;
}

.dark .send-button:hover:not(:disabled) {
  background: #1d4ed8;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #6b7280;
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
