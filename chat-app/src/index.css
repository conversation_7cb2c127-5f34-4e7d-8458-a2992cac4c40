@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply m-0 p-0 box-border;
  }

  body {
    @apply font-sans leading-relaxed h-screen overflow-hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    @apply h-screen w-screen;
  }
}

@layer components {
  .app {
    @apply h-screen grid grid-cols-[3fr_9fr] font-sans overflow-hidden relative;
    background:
      radial-gradient(ellipse at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 40%),
      radial-gradient(ellipse at 70% 60%, rgba(255, 255, 255, 0.12) 0%, transparent 35%),
      radial-gradient(ellipse at 20% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 30%),
      radial-gradient(ellipse at 80% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 25%),
      radial-gradient(ellipse at 50% 90%, rgba(255, 255, 255, 0.06) 0%, transparent 20%),
      radial-gradient(ellipse at 40% 10%, rgba(135, 206, 235, 0.3) 0%, transparent 50%),
      radial-gradient(ellipse at 80% 70%, rgba(100, 149, 237, 0.25) 0%, transparent 45%),
      linear-gradient(135deg, #1e3a8a 0%, #1e40af 25%, #2563eb 50%, #3b82f6 75%, #60a5fa 100%);
    background-size:
      800px 400px,
      600px 300px,
      400px 200px,
      500px 250px,
      300px 150px,
      1000px 500px,
      800px 400px,
      100% 100%;
    background-attachment: fixed;
    animation: cloudDrift 30s ease-in-out infinite;
  }

  .sidebar {
    @apply flex flex-col gap-8 p-6 overflow-y-auto;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(25px);
    border-right: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.2),
      inset -1px 0 0 rgba(255, 255, 255, 0.2);
  }

  .brand-section {
    @apply pb-8;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  }

  .logo-container {
    @apply flex items-center gap-3 mb-4;
  }

  .logo-icon {
    @apply w-10 h-10;
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.3));
  }

  .brand-section h1 {
    @apply text-white text-xl font-bold tracking-tight m-0 font-sans;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    @apply text-white/80 text-sm font-normal tracking-wide mb-6 opacity-90 leading-relaxed;
  }

  .chat-history {
    @apply flex-1;
  }

  .chat-history h3 {
    @apply text-white/90 text-base font-bold tracking-wide mb-4 opacity-90;
  }

  .chat-list {
    @apply flex flex-col gap-2;
  }

  .chat-item {
    @apply flex flex-col p-4 rounded-lg cursor-pointer transition-all duration-300;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .chat-item:hover {
    @apply transform translate-x-1;
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .chat-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }

  .chat-title {
    @apply text-white/90 text-sm font-semibold mb-1;
  }

  .chat-time {
    @apply text-white/70 text-xs font-normal opacity-70;
  }

  .status {
    @apply flex items-center gap-2;
  }

  .status-indicator {
    @apply flex items-center gap-2 px-4 py-2 rounded-md text-xs font-bold tracking-widest uppercase border;
  }

  .status-indicator.connected {
    @apply bg-green-500/10 text-green-500 border-green-500/30;
  }

  .status-indicator.disconnected {
    @apply bg-red-500/10 text-red-500 border-red-500/30;
  }

  .status-dot {
    @apply w-1.5 h-1.5 rounded-full bg-current animate-pulse;
  }

  .chat-container {
    @apply flex flex-col m-8 rounded-2xl overflow-hidden;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .messages {
    @apply flex-1 p-8 overflow-y-auto flex flex-col gap-4;
    max-height: calc(100vh - 200px);
  }

  .message {
    @apply flex flex-col max-w-4xl;
  }

  .user-message {
    @apply self-end;
  }

  .ai-message {
    @apply self-start;
  }

  .user-message .message-content {
    @apply bg-blue-500 text-white px-6 py-4 rounded-3xl border border-blue-400/30;
    border-radius: 20px 20px 5px 20px;
    box-shadow:
      0 4px 20px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .ai-message .message-content {
    @apply bg-white/80 text-slate-800 px-6 py-4 border border-blue-300/30 rounded-3xl;
    border-radius: 20px 20px 20px 5px;
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
  }

  .system {
    @apply self-center mx-auto max-w-4xl;
  }

  .system .message-content {
    @apply bg-blue-500/5 text-blue-500/70 px-4 py-2 rounded-lg text-sm text-center max-w-full border border-blue-500/15 font-normal tracking-wide opacity-80;
  }

  .system .message-time {
    @apply hidden;
  }

  .message-time {
    @apply text-xs text-gray-400 mt-2;
  }

  .user-message .message-time {
    @apply self-end;
  }

  .ai-message .message-time {
    @apply self-start;
  }

  .input-container {
    @apply flex gap-4 items-center px-8 py-6;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
  }

  .message-input {
    @apply flex-1 px-6 py-4 border rounded-xl text-base outline-none transition-all duration-300 text-white font-sans;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.2);
  }

  .message-input::placeholder {
    @apply text-white/70;
  }

  .message-input:focus {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow:
      0 0 0 2px rgba(255, 255, 255, 0.2),
      0 0 20px rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.25);
  }

  .message-input:disabled {
    @apply opacity-60 cursor-not-allowed;
    background: rgba(255, 255, 255, 0.1);
  }

  .send-button {
    @apply px-6 py-4 bg-blue-500 text-white rounded-xl font-medium transition-all duration-300 border border-blue-400/30;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
  }

  .send-button:hover:not(:disabled) {
    @apply bg-blue-600 transform -translate-y-0.5;
    box-shadow: 0 6px 25px rgba(59, 130, 246, 0.3);
  }

  .send-button:disabled {
    @apply opacity-50 cursor-not-allowed bg-gray-500;
  }

  .loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mr-2;
  }
}

@keyframes cloudDrift {
  0%,
  100% {
    background-position:
      0% 0%,
      100% 100%,
      50% 50%,
      0% 0%,
      40% 70%,
      10% 90%,
      30% 60%,
      60% 40%,
      0% 0%;
  }
  50% {
    background-position:
      20% 10%,
      80% 90%,
      30% 70%,
      10% 90%,
      30% 60%,
      60% 40%,
      0% 0%;
  }
}
