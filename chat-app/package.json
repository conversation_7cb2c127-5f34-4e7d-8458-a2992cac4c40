{"name": "chat-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --fix", "lint:check": "eslint .", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.4.2", "tailwindcss": "^4.1.11", "typescript": "^5.7.3", "vite": "^7.0.0"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix"]}}