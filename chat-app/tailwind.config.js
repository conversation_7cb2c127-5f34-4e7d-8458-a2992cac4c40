/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        flightlogger: {
          blue: {
            50: '#f0f8ff',
            100: '#e0f6ff',
            200: '#b6e5ff',
            300: '#87ceeb',
            400: '#60a5fa',
            500: '#3b82f6',
            600: '#2563eb',
            700: '#1e40af',
            800: '#1e3a8a',
            900: '#1e293b',
          },
        },
      },
      fontFamily: {
        sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'sans-serif'],
      },
      backdropBlur: {
        '25': '25px',
      },
      animation: {
        'cloud-drift': 'cloudDrift 30s ease-in-out infinite',
        'pulse-slow': 'pulse 2s infinite',
      },
      keyframes: {
        cloudDrift: {
          '0%, 100%': {
            backgroundPosition: '0% 0%, 100% 100%, 50% 50%, 0% 0%, 40% 70%, 10% 90%, 30% 60%, 60% 40%, 0% 0%',
          },
          '50%': {
            backgroundPosition: '20% 10%, 80% 90%, 30% 70%, 10% 90%, 30% 60%, 60% 40%, 0% 0%',
          },
        },
      },
    },
  },
  plugins: [],
}
