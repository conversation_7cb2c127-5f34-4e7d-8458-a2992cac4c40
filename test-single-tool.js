const { chromium } = require('playwright');

async function testSingleTool() {
  console.log('🧪 Testing Single MCP Tool - ListAircraftTool');

  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();

  try {
    // Navigate to chat interface
    await page.goto('http://localhost:3001');
    console.log('✅ Navigated to chat interface');

    // Wait for connection
    await page.waitForSelector('.status-indicator:has-text("ONLINE")', { timeout: 10000 });
    console.log('✅ Chat interface is ONLINE');

    // Count initial messages
    const initialMessageCount = await page.locator('.message').count();
    console.log('📋 Initial message count:', initialMessageCount);

    // Send test message
    console.log('\n📝 Testing ListAircraftTool...');
    await page.fill('.message-input', 'List all available aircraft');
    await page.click('.send-button');
    console.log('✅ Sent message');

    // Wait for loading spinner to appear
    await page.waitForSelector('.loading-spinner', { timeout: 5000 });
    console.log('✅ LLM is processing request');

    // Wait for loading spinner to disappear
    await page.waitForSelector('.loading-spinner', { state: 'hidden', timeout: 60000 });
    console.log('✅ LLM finished processing');

    // Wait for new message to appear
    await page.waitForFunction(
      (initialCount) => document.querySelectorAll('.message').length > initialCount,
      initialMessageCount,
      { timeout: 10000 }
    );
    console.log('✅ New message appeared');

    // Get all messages and find the last one
    const allMessages = await page.locator('.message').all();
    console.log('📋 Total messages now:', allMessages.length);

    // Get the last message content
    const lastMessage = allMessages[allMessages.length - 1];
    const messageContent = await lastMessage.locator('.message-content').textContent().catch(() => 'NO_CONTENT');
    console.log('📋 Response:', messageContent.substring(0, 300) + '...');

    if (messageContent.includes('AC-A') || messageContent.includes('aircraft') || messageContent.includes('Aircraft')) {
      console.log('✅ SUCCESS: ListAircraftTool working correctly!');
      console.log('🎯 MCP-LLM Integration is FUNCTIONAL');
    } else {
      console.log('❌ FAILED: ListAircraftTool response unexpected');
      console.log('Full response:', messageContent);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testSingleTool();
