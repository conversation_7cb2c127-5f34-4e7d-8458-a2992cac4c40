# frozen_string_literal: true

class Api::PublicApiController < ActionController::API
  include AbstractController::Translation
  include ActionController::Helpers
  include ActionController::Cookies if Rails.env.test? # authlogic breaks creation of users without this.
  include Pundit::Authorization
  include PublicTokenAuthentication
  include PublicApiDevOverride if Rails.env.development?
  include RateLimiting
  include FeatureContextConcern
  include FormDataConcern
  include ApplicationTracking
  include RequestContextConcern
  include S3Upload

  prepend_before_action :introspection, if: -> { introspection? } # ensure everybody without any token can introspect
  before_action :ensure_feature_enabled
  before_action :initialize_account, if: -> { current_account.present? }
  before_action :setup_tracking
  after_action :clear_tracking
  before_action :set_last_request_at

  def introspection?
    permitted_params[:query]&.include?('__schema')
  end

  def can?(action = nil, subject = nil)
    # Public API has super user permissions - always return true
    true
  end

  def authorize(record, query = nil)
    # Public API bypasses authorization checks - always allow
    true
  end

  def execute
    return if introspection?

    logger.tagged(current_account&.subdomain || 'my', [current_member&.id, current_user&.id].join(':')) do
      response, diff = method_timer { ::PublicFlightloggerSchema.execute(query, variables: variables, context: context) }
      if diff > ENV.fetch('PUBLIC_API_MAX_EXECUTION_TIME', 3.0).to_f
        report_long_running_query(diff)
      end

      render json: response
    end
  end

  private

  # rubocop:disable Rails/SkipsModelValidations
  def set_last_request_at
    current_time = Time.current
    Current.member.update_columns(last_request_at: current_time) if Current.member.is_a?(Member) && Current.member&.persisted?
    Current.user.update_columns(last_request_at: current_time) if Current.user.is_a?(User) && Current.user&.persisted?
  end
  # rubocop:enable Rails/SkipsModelValidations

  def json_error(code)
    { error: { message: t("public_api.errors.#{code}"), code: code } }
  end

  def report_long_running_query(execution_time)
    Sentry.set_extras(query_run_time_seconds: execution_time)
    Sentry.capture_message('Public API: Long running query')
  end

  def variables
    @variables ||= begin
                     vars = ensure_hash(permitted_params[:variables])
                     if vars.present?
                       NewRelic::Agent.add_custom_attributes(variables: vars.to_h)
                       Sentry.set_extras(gql_variables: vars.to_h)
                     end
                     vars
                   end
  end

  def query
    @query ||= begin
                 q = permitted_params[:query]
                 if q.present?
                   NewRelic::Agent.add_custom_attributes(query: q)
                   Sentry.set_extras(gql_query: q)
                 end
                 q
               end
  end

  def permitted_params
    params.permit(:query, :operationName, :subdomain, :format, variables: {}, public_api: [:query, :operationName, :format, { variables: {} }])
  end

  def ensure_feature_enabled
    render json: json_error(:unauthorized) unless feature_enabled?(:api_enabled)
  end

  def introspection
    render json: ::PublicFlightloggerSchema.execute(
      permitted_params[:query],
      variables: permitted_params[:variables],
      context: { introspection: true },
      operation_name: permitted_params[:operationName],
      max_depth: nil,
      max_complexity: nil,
    )
  end

  def context
    { current_user: current_user,
      current_member: current_member,
      current_account: current_account,
      key: key,
      controller: self }
  end

  def method_timer
    start = Time.current
    b = yield
    finish = Time.current
    diff = finish - start
    [b, diff]
  end

  def initialize_account
    AccountInitializer.new(self)
  end
end
