const { chromium } = require('playwright');

async function testBasicChat() {
  console.log('🚀 Testing Basic Chat Interface...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen to console messages
  page.on('console', msg => {
    console.log(`🖥️  Console [${msg.type()}]:`, msg.text());
  });
  
  try {
    console.log('📱 Navigating to chat app...');
    await page.goto('http://localhost:3001');
    
    console.log('⏳ Waiting for page to load...');
    await page.waitForTimeout(3000);
    
    console.log('📸 Taking screenshot...');
    await page.screenshot({ path: 'basic-chat-test.png', fullPage: true });
    
    // Check if any elements are visible
    const title = await page.title();
    console.log('📄 Page title:', title);
    
    const bodyText = await page.textContent('body');
    console.log('📝 Body text (first 200 chars):', bodyText?.substring(0, 200));
    
    // Try to find common elements
    const elements = await page.$$('*');
    console.log('🔍 Total elements found:', elements.length);
    
    // Check for specific selectors
    const selectors = [
      'h1', 'h2', 'h3',
      '[data-testid="connection-status"]',
      '[data-testid="message-input"]',
      '[data-testid="send-button"]',
      '.chat-container',
      '.message',
      'input',
      'button'
    ];
    
    for (const selector of selectors) {
      const found = await page.$(selector);
      console.log(`🔍 ${selector}: ${found ? '✅ Found' : '❌ Not found'}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    await page.screenshot({ path: 'basic-chat-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

testBasicChat();
