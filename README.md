# FlightLogger MCP Server

A Model Context Protocol (MCP) server implementation for FlightLogger, providing AI assistants with access to aircraft management and maintenance booking capabilities.

## Features

- **Aircraft Management**: List and find aircraft in your fleet
- **Maintenance Booking**: Create and manage maintenance appointments
- **Conflict Detection**: Check for scheduling conflicts
- **FlightLogger Integration**: Direct integration with FlightLogger's GraphQL API

## Architecture

This implementation uses FastMCP (Ruby) with HTTP-to-STDIO bridge architecture:

- **STDIO Transport**: Core MCP server using JSON-RPC communication
- **HTTP Bridge**: Node.js bridge server converts HTTP requests to STDIO
- **Docker Support**: Containerized deployment with health checks
- **FlightLogger Network**: Seamless integration with existing FlightLogger infrastructure

## Quick Start

### Prerequisites

- Docker and Docker Compose
- FlightLogger API access (API key and base URL)

### Setup

1. **Clone and configure**:
   ```bash
   git clone <repository-url>
   cd mcp-server
   cp .env.example .env
   ```

2. **Configure environment**:
   Edit `.env` with your FlightLogger credentials:
   ```
   FLIGHTLOGGER_API_KEY=your_api_key_here
   FLIGHTLOGGER_BASE_URL=https://api.flightlogger.net
   ```

3. **Start services**:
   ```bash
   docker-compose up -d
   ```

4. **Test the connection**:
   ```bash
   curl http://localhost:8080/health
   ```

## Services

### MCP Server (`mcp`)
- **Port**: 8080
- **Transport**: HTTP-to-STDIO bridge
- **Health Check**: `/health` endpoint
- **Tools**: 4 registered MCP tools for aircraft and maintenance management

### Chat Interface (`chat`)
- **Port**: 3001
- **Framework**: React + TypeScript + Vite
- **Features**: Real-time chat with MCP tool integration

### LLM Service (`llm`)
- **Port**: 11434
- **Engine**: Ollama
- **Model**: Configurable (default: llama3.2)

## MCP Tools

### 1. List Aircraft (`list_aircraft`)
Retrieve all aircraft in your fleet with filtering options.

**Parameters**:
- `limit` (optional): Maximum number of aircraft to return
- `offset` (optional): Number of aircraft to skip

### 2. Find Aircraft (`find_aircraft`)
Search for specific aircraft by registration or other criteria.

**Parameters**:
- `query`: Search term (registration, model, etc.)

### 3. Create Maintenance Booking (`create_maintenance_booking`)
Schedule maintenance appointments for aircraft.

**Parameters**:
- `aircraft_id`: ID of the aircraft
- `start_time`: Start time (ISO 8601 format)
- `end_time`: End time (ISO 8601 format)
- `description`: Maintenance description
- `maintenance_type`: Type of maintenance

### 4. Check Maintenance Conflicts (`check_maintenance_conflicts`)
Verify if a proposed maintenance booking conflicts with existing schedules.

**Parameters**:
- `aircraft_id`: ID of the aircraft
- `start_time`: Proposed start time
- `end_time`: Proposed end time

## Development

### Essential Files

- `mcp-server/lib/mcp_server.rb`: Core MCP server implementation
- `mcp-server/bridge-server.js`: HTTP-to-STDIO bridge
- `mcp-server/bin/server_stdio`: STDIO server binary
- `mcp-server/docker-entrypoint.sh`: Container startup script
- `chat-app/`: React TypeScript chat interface

## Configuration

### Environment Variables

- `FLIGHTLOGGER_API_KEY`: Your FlightLogger API key
- `FLIGHTLOGGER_BASE_URL`: FlightLogger API base URL
- `PORT`: HTTP server port (default: 8080)

### FlightLogger Integration

The server automatically configures host entries for local FlightLogger development:
- `api.flightlogger.test`
- `demo.flightlogger.test`
- `flightlogger.test`

## Troubleshooting

### Health Checks

All services include health checks:
- MCP Server: `curl http://localhost:8080/health`
- Chat Interface: `curl http://localhost:3001`
- Ollama: `docker-compose ps`

### Logs

View service logs:
```bash
docker-compose logs mcp
docker-compose logs chat
docker-compose logs llm
```
